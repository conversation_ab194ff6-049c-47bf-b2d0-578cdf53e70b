// TypeScript interfaces for Demo Requests matching the database schema

export type DemoRequestStatus = 'pending' | 'scheduled' | 'in-progress' | 'completed' | 'cancelled';

export type CompanySize = '1-10' | '10-50' | '50-200' | '200-500' | '500+';

export type BudgetRange = 
  | 'Under $10,000'
  | '$10,000 - $25,000'
  | '$25,000 - $50,000'
  | '$50,000 - $100,000'
  | '$100,000+';

export interface DemoRequest {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  company_name: string;
  job_title?: string;
  company_size?: CompanySize;
  industry?: string;
  project_type: string;
  budget_range?: BudgetRange;
  timeline?: string;
  project_description: string;
  specific_requirements?: string;
  preferred_demo_date?: string;
  preferred_demo_time?: string;
  how_did_you_hear?: string;
  status: DemoRequestStatus;
  assigned_to?: string;
  demo_scheduled_at?: string;
  notes?: string;
  created_at: string;
  updated_at?: string;
}

// Type for creating a new demo request (without auto-generated fields)
export interface CreateDemoRequest {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  company_name: string;
  job_title?: string;
  company_size?: CompanySize;
  industry?: string;
  project_type: string;
  budget_range?: BudgetRange;
  timeline?: string;
  project_description: string;
  specific_requirements?: string;
  preferred_demo_date?: string;
  preferred_demo_time?: string;
  how_did_you_hear?: string;
  status?: DemoRequestStatus;
  assigned_to?: string;
  demo_scheduled_at?: string;
  notes?: string;
}

// Type for updating a demo request (all fields optional except id)
export interface UpdateDemoRequest {
  id: number;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  company_name?: string;
  job_title?: string;
  company_size?: CompanySize;
  industry?: string;
  project_type?: string;
  budget_range?: BudgetRange;
  timeline?: string;
  project_description?: string;
  specific_requirements?: string;
  preferred_demo_date?: string;
  preferred_demo_time?: string;
  how_did_you_hear?: string;
  status?: DemoRequestStatus;
  assigned_to?: string;
  demo_scheduled_at?: string;
  notes?: string;
}

// Query parameters for filtering demo requests
export interface DemoRequestFilters {
  search?: string;
  status?: DemoRequestStatus | 'all';
  budget_range?: BudgetRange | 'all';
  company_size?: CompanySize | 'all';
  industry?: string | 'all';
  assigned_to?: string;
  created_after?: string;
  created_before?: string;
  demo_date_after?: string;
  demo_date_before?: string;
}

// Query parameters for demo requests list
export interface DemoRequestListParams extends DemoRequestFilters {
  page?: number;
  page_size?: number;
  ordering?: string;
}

// Bulk operations
export interface BulkUpdateDemoRequestsRequest {
  ids: number[];
  data: {
    status?: DemoRequestStatus;
    assigned_to?: string;
    notes?: string;
  };
}

export interface BulkDeleteDemoRequestsRequest {
  ids: number[];
}

// Statistics and analytics
export interface DemoRequestStats {
  total_count: number;
  status_counts: Record<DemoRequestStatus, number>;
  recent_count: number;
  conversion_rate: number;
  average_response_time: number;
  top_industries: Array<{
    industry: string;
    count: number;
  }>;
  budget_distribution: Record<BudgetRange, number>;
  monthly_trends: Array<{
    month: string;
    count: number;
    completed: number;
  }>;
}

// Export options
export interface ExportDemoRequestsRequest {
  format: 'csv' | 'xlsx' | 'pdf';
  filters?: DemoRequestFilters;
  fields?: string[];
}

// Helper functions
export const getDemoRequestStatusColor = (status: DemoRequestStatus): string => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'scheduled':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'in-progress':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'completed':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export const getDemoRequestStatusLabel = (status: DemoRequestStatus): string => {
  switch (status) {
    case 'pending':
      return 'Pending';
    case 'scheduled':
      return 'Scheduled';
    case 'in-progress':
      return 'In Progress';
    case 'completed':
      return 'Completed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return 'Unknown';
  }
};
