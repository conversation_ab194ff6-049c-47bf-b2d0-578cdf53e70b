// Demo Requests service layer exports
// This provides a clean interface for importing demo request functionality

// Query hooks
export {
  useDemoRequests,
  useInfiniteDemoRequests,
  useDemoRequest,
  useDemoRequestStats,
  useRecentDemoRequests,
  useDemoRequestsByStatus,
  useDemoRequestsByAssignee,
  useSearchDemoRequests,
} from '../queries/demoRequests';

// Mutation hooks
export {
  useCreateDemoRequest,
  useUpdateDemoRequest,
  useDeleteDemoRequest,
  useBulkUpdateDemoRequests,
  useBulkDeleteDemoRequests,
  useExportDemoRequests,
  useAssignDemoRequest,
  useUpdateDemoRequestStatus,
} from '../mutations/demoRequests';

// Types
export type {
  DemoRequest,
  CreateDemoRequest,
  UpdateDemoRequest,
  DemoRequestStatus,
  CompanySize,
  BudgetRange,
  DemoRequestFilters,
  DemoRequestListParams,
  BulkUpdateDemoRequestsRequest,
  BulkDeleteDemoRequestsRequest,
  DemoRequestStats,
  ExportDemoRequestsRequest,
} from '../../types/demoRequest';

// Helper functions
export {
  getDemoRequestStatusColor,
  getDemoRequestStatusLabel,
} from '../../types/demoRequest';
