// Common API types for Django REST Framework integration

// Base pagination response structure from Django REST Framework
export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Standard API response wrapper
export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

// Error response structure
export interface ApiError {
  message: string;
  status: number;
  details?: Record<string, string[]>;
}

// Query parameters for list endpoints
export interface ListQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  ordering?: string;
}

// Common filter parameters
export interface FilterParams {
  is_active?: boolean;
  is_featured?: boolean;
  created_at_after?: string;
  created_at_before?: string;
}

// Bulk operation types
export interface BulkUpdateRequest<T> {
  ids: number[];
  data: Partial<T>;
}

export interface BulkDeleteRequest {
  ids: number[];
}

// File upload types
export interface FileUploadResponse {
  id: string;
  url: string;
  filename: string;
  size: number;
  content_type: string;
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
    is_staff: boolean;
    is_superuser: boolean;
  };
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  access_token: string;
}

// Generic CRUD operation types
export interface CreateRequest<T> {
  data: T;
}

export interface UpdateRequest<T> {
  id: number;
  data: Partial<T>;
}

export interface DeleteRequest {
  id: number;
}

// Status types for various entities
export type RequestStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';
export type PublicationStatus = 'draft' | 'published' | 'archived';

// Common entity fields that appear across multiple models
export interface BaseEntity {
  id: number;
  created_at: string;
  updated_at: string;
}

export interface ActivatableEntity extends BaseEntity {
  is_active: boolean;
}

export interface FeaturedEntity extends ActivatableEntity {
  is_featured: boolean;
}

export interface OrderedEntity extends BaseEntity {
  display_order: number;
}

// Validation error structure from Django REST Framework
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationErrorResponse {
  non_field_errors?: string[];
  [fieldName: string]: string[] | undefined;
}

// Search and filtering types
export interface SearchParams {
  q?: string;
  category?: string;
  tags?: string[];
  date_from?: string;
  date_to?: string;
}

// Export utility type for making all properties optional except id
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;
