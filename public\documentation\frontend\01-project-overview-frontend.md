# Bean Software Frontend - Project Overview

## Introduction

Bean Software Frontend is a modern React-based admin panel built with TypeScript and Vite. The application serves as the administrative interface for Bean Software's digital presence, providing comprehensive content management, user administration, and system monitoring capabilities.

## Architecture Overview

The frontend follows a modern React architecture with several key patterns:

- **Component-Based Architecture**: Modular, reusable components
- **TypeScript Integration**: Full type safety across the application
- **State Management**: Context API and custom hooks for state management
- **Routing**: React Router for client-side navigation
- **UI Framework**: Tailwind CSS with shadcn/ui components

## Technology Stack

- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Icons**: Lucide React
- **Routing**: React Router DOM
- **Development Server**: Vite dev server (port 5173)

## Project Structure

```
bean-admin-panel-frontend/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # shadcn/ui components
│   │   ├── layout/         # Layout components
│   │   └── common/         # Common components
│   ├── pages/              # Page components
│   │   ├── admin/          # Admin panel pages
│   │   │   ├── dashboard/  # Dashboard components
│   │   │   ├── content/    # Content management
│   │   │   ├── team/       # Team management
│   │   │   ├── technologies/ # Technology showcase
│   │   │   ├── demos/      # Demo projects
│   │   │   └── documentation/ # Documentation system
│   │   └── auth/           # Authentication pages
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility libraries
│   ├── types/              # TypeScript type definitions
│   ├── data/               # Mock data and constants
│   └── styles/             # Global styles
├── public/                 # Static assets
├── documentation/          # Project documentation
│   ├── frontend/           # Frontend documentation
│   └── backend/            # Backend documentation
└── config files           # Vite, TypeScript, Tailwind configs
```

## Key Features

### 1. Modern Admin Interface
- Clean, responsive design
- Dark/light theme support
- Intuitive navigation and user experience

### 2. Content Management System
- Multi-language content support
- Rich text editing capabilities
- Media management

### 3. Team Management
- Team member profiles
- Role-based access control
- Activity tracking

### 4. Technology Showcase
- Technology stack display
- Project demonstrations
- Portfolio management

### 5. Development Tools
- Built-in documentation system
- Component library
- Development utilities

## Development Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager

### Quick Start
```bash
# Navigate to project directory
cd bean-admin-panel-frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

### Access Points
- **Main Application**: http://localhost:5173/
- **Admin Panel**: http://localhost:5173/admin/
- **Documentation**: http://localhost:5173/admin/documentation

## Configuration

### Key Configuration Files
- **vite.config.ts**: Vite build configuration
- **tailwind.config.js**: Tailwind CSS configuration
- **tsconfig.json**: TypeScript configuration
- **package.json**: Dependencies and scripts

### Environment Variables
- Development environment uses default Vite settings
- Production builds are optimized for deployment

This frontend architecture provides a robust foundation for a modern admin panel with excellent developer experience and user interface design.
