import { ReactNode, useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { documentationSections } from '../config';
import { MarkdownRenderer } from './MarkdownRenderer';

interface DocContentProps {
  activeSection: string;
  activeSubsection?: string;
  className?: string;
  docType?: 'frontend' | 'backend';
}

interface ContentSectionProps {
  title: string;
  children: ReactNode;
  badge?: string;
}

function ContentSection({ title, children, badge }: ContentSectionProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3">
        <h2 className="text-2xl font-bold text-foreground">{title}</h2>
        {badge && <Badge variant="secondary">{badge}</Badge>}
      </div>
      <Separator />
      <div className="prose prose-slate max-w-none dark:prose-invert">
        {children}
      </div>
    </div>
  );
}

function PlaceholderContent({ title, description }: { title: string; description?: string }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {title}
          <Badge variant="outline">Coming Soon</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-muted-foreground">
            {description || `Documentation for ${title} will be available soon.`}
          </p>
          <div className="bg-muted/30 rounded-lg p-6 text-center">
            <p className="text-sm text-muted-foreground">
              📝 Content is being prepared for this section
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function DocContent({ activeSection, activeSubsection, className, docType = 'frontend' }: DocContentProps) {
  const currentSection = documentationSections.find(section => section.id === activeSection);
  const currentSubsection = currentSection?.subsections?.find(
    subsection => subsection.id === activeSubsection
  );

  // If we have a subsection, show that content
  if (currentSubsection) {
    return (
      <ScrollArea className={cn("flex-1 h-full", className)}>
        <div className="p-8 max-w-4xl mx-auto">
          <ContentSection 
            title={currentSubsection.title}
            badge={currentSection?.title}
          >
            <PlaceholderContent 
              title={currentSubsection.title}
              description={`Detailed documentation for ${currentSubsection.title} in the ${currentSection?.title} section.`}
            />
          </ContentSection>
        </div>
      </ScrollArea>
    );
  }

  // If we have a section but no subsection, show section overview
  if (currentSection) {
    // Show backend markdown documentation for overview section
    if (docType === 'backend' && activeSection === 'overview') {
      return (
        <ScrollArea className={cn("flex-1 h-full", className)}>
          <div className="p-8 max-w-4xl mx-auto">
            <MarkdownRenderer
              filePath="backend/01-project-overview-backend.md"
              className="w-full"
            />
          </div>
        </ScrollArea>
      );
    }

    // Show frontend markdown documentation for overview section
    if (docType === 'frontend' && activeSection === 'overview') {
      return (
        <ScrollArea className={cn("flex-1 h-full", className)}>
          <div className="p-8 max-w-4xl mx-auto">
            <MarkdownRenderer
              filePath="frontend/01-project-overview-frontend.md"
              className="w-full"
            />
          </div>
        </ScrollArea>
      );
    }

    return (
      <ScrollArea className={cn("flex-1 h-full", className)}>
        <div className="p-8 max-w-4xl mx-auto">
          <ContentSection title={currentSection.title}>
            <div className="space-y-6">
              <PlaceholderContent
                title={`${currentSection.title} Overview`}
                description={`Overview and introduction to the ${currentSection.title} section.`}
              />
              
              {currentSection.subsections && currentSection.subsections.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Section Contents</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-3">
                      {currentSection.subsections.map((subsection) => (
                        <div 
                          key={subsection.id}
                          className="flex items-center justify-between p-3 rounded-lg border border-border hover:bg-accent/30 transition-colors"
                        >
                          <div>
                            <h4 className="font-medium">{subsection.title}</h4>
                            <p className="text-sm text-muted-foreground">
                              Documentation for {subsection.title.toLowerCase()}
                            </p>
                          </div>
                          <Badge variant="outline">Coming Soon</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </ContentSection>
        </div>
      </ScrollArea>
    );
  }

  // Default welcome content
  return (
    <ScrollArea className={cn("flex-1 h-full", className)}>
      <div className="p-8 max-w-4xl mx-auto">
        <ContentSection title="Welcome to Documentation">
          <Card>
            <CardHeader>
              <CardTitle>
                {docType === 'backend' ? 'Backend Documentation' : 'Frontend Documentation'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Welcome to the comprehensive documentation for the {docType === 'backend' ? 'Backend API and Django application' : 'Frontend Admin Panel'}.
                  This documentation covers everything you need to know about the project,
                  from getting started to advanced development topics.
                </p>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="p-4 rounded-lg border border-border">
                    <h4 className="font-semibold mb-2">🚀 Getting Started</h4>
                    <p className="text-sm text-muted-foreground">
                      Learn how to set up and run the project locally
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border border-border">
                    <h4 className="font-semibold mb-2">🏗️ Architecture</h4>
                    <p className="text-sm text-muted-foreground">
                      Understand the project structure and tech stack
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border border-border">
                    <h4 className="font-semibold mb-2">🧩 Components</h4>
                    <p className="text-sm text-muted-foreground">
                      Explore the UI components and their usage
                    </p>
                  </div>
                  
                  <div className="p-4 rounded-lg border border-border">
                    <h4 className="font-semibold mb-2">🎨 Styling</h4>
                    <p className="text-sm text-muted-foreground">
                      Learn about the design system and theming
                    </p>
                  </div>
                </div>
                
                <div className="mt-6 p-4 bg-primary/5 rounded-lg border border-primary/20">
                  <p className="text-sm">
                    <strong>Note:</strong> This documentation is currently being developed. 
                    Use the sidebar navigation to explore different sections as they become available.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </ContentSection>
      </div>
    </ScrollArea>
  );
}
