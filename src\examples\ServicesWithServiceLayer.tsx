// Example of how to use the Services service layer in the Services.tsx component
// This demonstrates the migration from mock data to API calls

import { useState } from "react";
import { Plus, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Import the service layer hooks
import { useServicesWithActions } from "@/hooks/useServicesWithActions";
import { useFeaturedServices, useServiceCategories } from "@/services";
import type { ServiceCategory } from "@/services";

// Import existing components
import { AddServiceDialog } from "@/components/admin/AddServiceDialog";
import { ViewServiceDialog } from "@/components/admin/ViewServiceDialog";

export default function ServicesWithServiceLayer() {
  // State for dialogs and UI
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit'>('add');
  const [selectedService, setSelectedService] = useState(null);

  // State for filters
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<ServiceCategory | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  // Use the service layer hooks
  const {
    services,
    totalCount,
    isLoading,
    isError,
    error,
    createService,
    updateService,
    deleteService,
    toggleFeatured,
    toggleActive,
    setSearch,
    setCategory,
    setActiveFilter,
    params,
  } = useServicesWithActions({
    search: searchTerm,
    category: categoryFilter === 'all' ? undefined : categoryFilter,
    is_active: statusFilter === 'all' ? undefined : statusFilter === 'active',
    page_size: 20,
  });

  // Additional queries
  const { data: featuredServices, isLoading: isFeaturedLoading } = useFeaturedServices();
  const { data: categories, isLoading: isCategoriesLoading } = useServiceCategories();

  // Event handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setSearch(value);
  };

  const handleCategoryFilter = (category: ServiceCategory | 'all') => {
    setCategoryFilter(category);
    setCategory(category);
  };

  const handleStatusFilter = (status: 'all' | 'active' | 'inactive') => {
    setStatusFilter(status);
    setActiveFilter(status === 'all' ? undefined : status === 'active');
  };

  const handleAddService = async (serviceData: any) => {
    try {
      await createService(serviceData);
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error('Failed to create service:', error);
    }
  };

  const handleUpdateService = async (serviceData: any) => {
    try {
      await updateService(serviceData);
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error('Failed to update service:', error);
    }
  };

  const handleDeleteService = async (id: number) => {
    try {
      await deleteService(id);
    } catch (error) {
      console.error('Failed to delete service:', error);
    }
  };

  const handleToggleFeatured = async (id: number, currentStatus: boolean) => {
    try {
      await toggleFeatured(id, !currentStatus);
    } catch (error) {
      console.error('Failed to toggle featured status:', error);
    }
  };

  const handleToggleActive = async (id: number, currentStatus: boolean) => {
    try {
      await toggleActive(id, !currentStatus);
    } catch (error) {
      console.error('Failed to toggle active status:', error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Services Management</h1>
        </div>
        <div className="text-center py-8">Loading services...</div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Services Management</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              Error loading services: {error?.message || 'Unknown error'}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Services Management</h1>
          <p className="text-muted-foreground">
            Manage your services and offerings ({totalCount} total)
          </p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Service
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search services..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={categoryFilter} onValueChange={handleCategoryFilter}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories?.map((cat) => (
                  <SelectItem key={cat.category} value={cat.category}>
                    {cat.category} ({cat.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Services Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {services.map((service) => (
          <Card key={service.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {service.title}
                    {service.is_featured && (
                      <Badge variant="secondary">Featured</Badge>
                    )}
                    <Badge variant={service.is_active ? "default" : "secondary"}>
                      {service.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </CardTitle>
                  <CardDescription>{service.short_description}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Category: {service.category}
                </p>
                {service.price_range && (
                  <p className="text-sm text-muted-foreground">
                    Price: {service.price_range}
                  </p>
                )}
                {service.duration && (
                  <p className="text-sm text-muted-foreground">
                    Duration: {service.duration}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Dialogs */}
      <AddServiceDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onAddService={handleAddService}
        onUpdateService={handleUpdateService}
        service={selectedService}
        mode={dialogMode}
      />

      <ViewServiceDialog
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
        service={selectedService}
      />
    </div>
  );
}
