// Custom hook that combines demo requests queries and mutations
// This demonstrates how to use the service layer in components

import { useState, useCallback } from 'react';
import { useToast } from './use-toast';
import {
  useDemoRequests,
  useCreateDemoRequest,
  useUpdateDemoRequest,
  useDeleteDemoRequest,
  useUpdateDemoRequestStatus,
  useAssignDemoRequest,
  type DemoRequestListParams,
  type CreateDemoRequest,
  type UpdateDemoRequest,
  type DemoRequestStatus,
} from '@/services';

export const useDemoRequestsWithActions = (initialParams?: DemoRequestListParams) => {
  const [params, setParams] = useState<DemoRequestListParams>(initialParams || {});
  const { toast } = useToast();

  // Queries
  const {
    data: demoRequestsData,
    isLoading,
    isError,
    error,
    refetch,
  } = useDemoRequests(params);

  // Mutations
  const createMutation = useCreateDemoRequest();
  const updateMutation = useUpdateDemoRequest();
  const deleteMutation = useDeleteDemoRequest();
  const updateStatusMutation = useUpdateDemoRequestStatus();
  const assignMutation = useAssignDemoRequest();

  // Actions
  const createDemoRequest = useCallback(async (data: CreateDemoRequest) => {
    try {
      const result = await createMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Demo request created successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create demo request",
        variant: "destructive",
      });
      throw error;
    }
  }, [createMutation, toast]);

  const updateDemoRequest = useCallback(async (data: UpdateDemoRequest) => {
    try {
      const result = await updateMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Demo request updated successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update demo request",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateMutation, toast]);

  const deleteDemoRequest = useCallback(async (id: number) => {
    try {
      await deleteMutation.mutateAsync(id);
      toast({
        title: "Success",
        description: "Demo request deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete demo request",
        variant: "destructive",
      });
      throw error;
    }
  }, [deleteMutation, toast]);

  const updateStatus = useCallback(async (id: number, status: DemoRequestStatus) => {
    try {
      const result = await updateStatusMutation.mutateAsync({ id, status });
      toast({
        title: "Success",
        description: `Demo request status updated to ${status}`,
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update status",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateStatusMutation, toast]);

  const assignToUser = useCallback(async (id: number, assignedTo: string) => {
    try {
      const result = await assignMutation.mutateAsync({ id, assignedTo });
      toast({
        title: "Success",
        description: `Demo request assigned to ${assignedTo}`,
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to assign demo request",
        variant: "destructive",
      });
      throw error;
    }
  }, [assignMutation, toast]);

  // Filter and search functions
  const updateFilters = useCallback((newParams: Partial<DemoRequestListParams>) => {
    setParams(prev => ({ ...prev, ...newParams, page: 1 })); // Reset to first page
  }, []);

  const clearFilters = useCallback(() => {
    setParams({});
  }, []);

  const setPage = useCallback((page: number) => {
    setParams(prev => ({ ...prev, page }));
  }, []);

  return {
    // Data
    demoRequests: demoRequestsData?.results || [],
    totalCount: demoRequestsData?.count || 0,
    hasNextPage: !!demoRequestsData?.next,
    hasPreviousPage: !!demoRequestsData?.previous,
    
    // Loading states
    isLoading,
    isError,
    error,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isUpdatingStatus: updateStatusMutation.isPending,
    isAssigning: assignMutation.isPending,
    
    // Actions
    createDemoRequest,
    updateDemoRequest,
    deleteDemoRequest,
    updateStatus,
    assignToUser,
    refetch,
    
    // Filters
    params,
    updateFilters,
    clearFilters,
    setPage,
  };
};
