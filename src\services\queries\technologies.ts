// TanStack Query hooks for Technologies
import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { apiClient } from '../api/client';
import { TECHNOLOGIES } from '../api/endpoints';
import { QUERY_KEYS } from '../config';
import type { PaginatedResponse } from '../api/types';
import type { Technology, TechnologyCategory } from '../../types/technology';

// Query parameters for technologies list
export interface TechnologyListParams {
  page?: number;
  page_size?: number;
  search?: string;
  category_id?: number;
  is_active?: boolean;
  is_featured?: boolean;
  ordering?: string;
}

// Query parameters for technology categories list
export interface TechnologyCategoryListParams {
  page?: number;
  page_size?: number;
  search?: string;
  is_active?: boolean;
  ordering?: string;
}

// Get paginated list of technologies
export const useTechnologies = (params?: TechnologyListParams) => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.LIST(params),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Technology>>(
        TECHNOLOGIES.LIST,
        params
      );
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes - technologies don't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get infinite scroll technologies (for large datasets)
export const useInfiniteTechnologies = (params?: Omit<TechnologyListParams, 'page'>) => {
  return useInfiniteQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.LIST({ ...params, infinite: true }),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await apiClient.get<PaginatedResponse<Technology>>(
        TECHNOLOGIES.LIST,
        { ...params, page: pageParam }
      );
      return response.data;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.next ? allPages.length + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Get single technology by ID
export const useTechnology = (id: number, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.DETAIL(id),
    queryFn: async () => {
      const response = await apiClient.get<Technology>(
        TECHNOLOGIES.DETAIL(id)
      );
      return response.data;
    },
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes - individual technologies are stable
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Get featured technologies
export const useFeaturedTechnologies = () => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.FEATURED,
    queryFn: async () => {
      const response = await apiClient.get<Technology[]>(
        TECHNOLOGIES.FEATURED
      );
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Get technologies by category
export const useTechnologiesByCategory = (categoryId: number) => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.BY_CATEGORY(categoryId.toString()),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Technology>>(
        TECHNOLOGIES.BY_CATEGORY(categoryId.toString()),
        { category_id: categoryId }
      );
      return response.data;
    },
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Get active technologies only
export const useActiveTechnologies = () => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.LIST({ is_active: true }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Technology>>(
        TECHNOLOGIES.LIST,
        { is_active: true }
      );
      return response.data;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Get technology categories
export const useTechnologyCategories = (params?: TechnologyCategoryListParams) => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<TechnologyCategory>>(
        TECHNOLOGIES.CATEGORIES,
        params
      );
      return response.data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes - categories don't change often
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};

// Get single technology category by ID
export const useTechnologyCategory = (id: number, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES, // Could be more specific
    queryFn: async () => {
      const response = await apiClient.get<TechnologyCategory>(
        `${TECHNOLOGIES.CATEGORIES}${id}/`
      );
      return response.data;
    },
    enabled: enabled && !!id,
    staleTime: 15 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
  });
};

// Get active technology categories only
export const useActiveTechnologyCategories = () => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<TechnologyCategory>>(
        TECHNOLOGIES.CATEGORIES,
        { is_active: true }
      );
      return response.data;
    },
    staleTime: 15 * 60 * 1000,
    gcTime: 60 * 60 * 1000,
  });
};

// Search technologies
export const useSearchTechnologies = (searchTerm: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.LIST({ search: searchTerm }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Technology>>(
        TECHNOLOGIES.LIST,
        { search: searchTerm }
      );
      return response.data;
    },
    enabled: enabled && !!searchTerm && searchTerm.length >= 2,
    staleTime: 30 * 1000, // 30 seconds - search results should be fresh
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get technologies for dashboard (recent, featured, etc.)
export const useTechnologiesForDashboard = () => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.LIST({ dashboard: true }),
    queryFn: async () => {
      const response = await apiClient.get<{
        total_count: number;
        active_count: number;
        featured_count: number;
        categories_count: number;
        recent_technologies: Technology[];
        popular_categories: Array<{ category: TechnologyCategory; count: number }>;
      }>(
        TECHNOLOGIES.LIST,
        { dashboard: true }
      );
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get technologies with specific ordering
export const useOrderedTechnologies = (ordering: string = '-created_at') => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.LIST({ ordering }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Technology>>(
        TECHNOLOGIES.LIST,
        { ordering }
      );
      return response.data;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Get technology statistics by category
export const useTechnologyStatsByCategory = () => {
  return useQuery({
    queryKey: QUERY_KEYS.TECHNOLOGIES.LIST({ stats: true }),
    queryFn: async () => {
      const response = await apiClient.get<Array<{
        category: TechnologyCategory;
        total_count: number;
        active_count: number;
        featured_count: number;
      }>>(
        TECHNOLOGIES.LIST,
        { stats: true }
      );
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};
