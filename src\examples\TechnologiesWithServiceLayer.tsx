// Example of how to use the Technologies service layer in the Technologies.tsx component
// This demonstrates the migration from mock data to API calls

import { useState } from "react";
import { Plus, Search, Code, Database, Smartphone, Globe, Settings } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Import the service layer hooks
import { useTechnologiesWithActions } from "@/hooks/useTechnologiesWithActions";
import { useFeaturedTechnologies, useTechnologiesByCategory } from "@/services";

// Import existing components
import { AddCategoryDialog } from "@/components/admin/AddCategoryDialog";

// Category icons mapping (this would be moved to a constants file)
const categoryIcons = {
  frontend: Globe,
  backend: Database,
  mobile: Smartphone,
  devops: Settings,
  default: Code,
};

const categoryColors = {
  frontend: "blue",
  backend: "green",
  mobile: "purple",
  devops: "orange",
  default: "gray",
};

export default function TechnologiesWithServiceLayer() {
  // State for dialogs and UI
  const [isAddCategoryDialogOpen, setIsAddCategoryDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<number>(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [featuredFilter, setFeaturedFilter] = useState<'all' | 'featured' | 'not-featured'>('all');

  // Use the service layer hooks
  const {
    technologies,
    categories,
    totalTechnologiesCount,
    totalCategoriesCount,
    isLoading,
    isError,
    error,
    createTechnology,
    updateTechnology,
    deleteTechnology,
    toggleFeatured,
    toggleActive,
    createCategory,
    updateCategory,
    deleteCategory,
    setSearch,
    setCategoryFilter,
    setActiveFilter,
    setFeaturedFilter,
  } = useTechnologiesWithActions({
    search: searchTerm,
    category_id: selectedCategory,
    is_active: statusFilter === 'all' ? undefined : statusFilter === 'active',
    is_featured: featuredFilter === 'all' ? undefined : featuredFilter === 'featured',
    page_size: 50,
  });

  // Additional queries
  const { data: featuredTechnologies, isLoading: isFeaturedLoading } = useFeaturedTechnologies();
  const { data: categoryTechnologies, isLoading: isCategoryLoading } = useTechnologiesByCategory(selectedCategory);

  // Event handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setSearch(value);
  };

  const handleCategorySelect = (categoryId: number) => {
    setSelectedCategory(categoryId);
    setCategoryFilter(categoryId);
  };

  const handleStatusFilter = (status: 'all' | 'active' | 'inactive') => {
    setStatusFilter(status);
    setActiveFilter(status === 'all' ? undefined : status === 'active');
  };

  const handleFeaturedFilter = (featured: 'all' | 'featured' | 'not-featured') => {
    setFeaturedFilter(featured);
    setFeaturedFilter(featured === 'all' ? undefined : featured === 'featured');
  };

  const handleAddCategory = async (categoryData: any) => {
    try {
      await createCategory(categoryData);
      setIsAddCategoryDialogOpen(false);
    } catch (error) {
      console.error('Failed to create category:', error);
    }
  };

  const handleToggleFeatured = async (id: number, currentStatus: boolean) => {
    try {
      await toggleFeatured(id, !currentStatus);
    } catch (error) {
      console.error('Failed to toggle featured status:', error);
    }
  };

  const handleToggleActive = async (id: number, currentStatus: boolean) => {
    try {
      await toggleActive(id, !currentStatus);
    } catch (error) {
      console.error('Failed to toggle active status:', error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Technologies Management</h1>
        </div>
        <div className="text-center py-8">Loading technologies...</div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Technologies Management</h1>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              Error loading technologies: {error?.message || 'Unknown error'}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const selectedCategoryData = categories.find(cat => cat.id === selectedCategory);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Technologies Management</h1>
          <p className="text-muted-foreground">
            Manage your technology stack ({totalTechnologiesCount} technologies, {totalCategoriesCount} categories)
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setIsAddCategoryDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Category
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Technology
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search technologies..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select value={featuredFilter} onValueChange={handleFeaturedFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Featured" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="featured">Featured</SelectItem>
                <SelectItem value="not-featured">Not Featured</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Categories</CardTitle>
              <CardDescription>Select a category to view technologies</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {categories.map((category) => {
                const IconComponent = categoryIcons[category.name as keyof typeof categoryIcons] || categoryIcons.default;
                const isSelected = category.id === selectedCategory;
                
                return (
                  <div
                    key={category.id}
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      isSelected 
                        ? 'bg-primary text-primary-foreground' 
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => handleCategorySelect(category.id)}
                  >
                    <div className="flex items-center gap-3">
                      <IconComponent className="w-5 h-5" />
                      <div className="flex-1">
                        <div className="font-medium">{category.display_name}</div>
                        <div className="text-sm opacity-70">
                          {category.technology_count || 0} technologies
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </div>

        {/* Technologies Grid */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {selectedCategoryData?.display_name} Technologies
                <Badge variant="secondary">
                  {technologies.length} technologies
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                {technologies.map((technology) => (
                  <Card key={technology.id} className="relative">
                    <CardHeader className="pb-2">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-lg">{technology.name}</CardTitle>
                        <div className="flex items-center space-x-1">
                          {technology.is_featured && (
                            <Badge variant="secondary" className="text-xs">Featured</Badge>
                          )}
                          <Badge variant={technology.is_active ? "default" : "secondary"} className="text-xs">
                            {technology.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {technology.description && (
                        <p className="text-sm text-muted-foreground mb-3">
                          {technology.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>Order: {technology.display_order}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add Category Dialog */}
      <AddCategoryDialog
        open={isAddCategoryDialogOpen}
        onOpenChange={setIsAddCategoryDialogOpen}
        onAddCategory={handleAddCategory}
        mode="add"
      />
    </div>
  );
}
