// TanStack Query hooks for Services
import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { apiClient } from '../api/client';
import { SERVICES } from '../api/endpoints';
import { QUERY_KEYS } from '../config';
import type { PaginatedResponse } from '../api/types';
import type { Service, ServiceCategory } from '../../types/service';

// Query parameters for services list
export interface ServiceListParams {
  page?: number;
  page_size?: number;
  search?: string;
  category?: ServiceCategory | 'all';
  is_active?: boolean;
  is_featured?: boolean;
  ordering?: string;
}

// Get paginated list of services
export const useServices = (params?: ServiceListParams) => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.LIST(params),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Service>>(
        SERVICES.LIST,
        params
      );
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes - services don't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get infinite scroll services (for large datasets)
export const useInfiniteServices = (params?: Omit<ServiceListParams, 'page'>) => {
  return useInfiniteQuery({
    queryKey: QUERY_KEYS.SERVICES.LIST({ ...params, infinite: true }),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await apiClient.get<PaginatedResponse<Service>>(
        SERVICES.LIST,
        { ...params, page: pageParam }
      );
      return response.data;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.next ? allPages.length + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Get single service by ID
export const useService = (id: number, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.DETAIL(id),
    queryFn: async () => {
      const response = await apiClient.get<Service>(
        SERVICES.DETAIL(id)
      );
      return response.data;
    },
    enabled: enabled && !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes - individual services are stable
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Get featured services
export const useFeaturedServices = () => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.FEATURED,
    queryFn: async () => {
      const response = await apiClient.get<Service[]>(
        SERVICES.FEATURED
      );
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Get services by category
export const useServicesByCategory = (category: ServiceCategory) => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.LIST({ category }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Service>>(
        SERVICES.LIST,
        { category }
      );
      return response.data;
    },
    enabled: !!category && category !== 'other',
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Get active services only
export const useActiveServices = () => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.LIST({ is_active: true }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Service>>(
        SERVICES.LIST,
        { is_active: true }
      );
      return response.data;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Get service categories (for filtering)
export const useServiceCategories = () => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.CATEGORIES,
    queryFn: async () => {
      const response = await apiClient.get<Array<{ category: ServiceCategory; count: number }>>(
        SERVICES.CATEGORIES
      );
      return response.data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes - categories don't change often
    gcTime: 60 * 60 * 1000, // 1 hour
  });
};

// Search services
export const useSearchServices = (searchTerm: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.LIST({ search: searchTerm }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Service>>(
        SERVICES.LIST,
        { search: searchTerm }
      );
      return response.data;
    },
    enabled: enabled && !!searchTerm && searchTerm.length >= 2,
    staleTime: 30 * 1000, // 30 seconds - search results should be fresh
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get services for dashboard (recent, featured, etc.)
export const useServicesForDashboard = () => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.LIST({ dashboard: true }),
    queryFn: async () => {
      const response = await apiClient.get<{
        total_count: number;
        active_count: number;
        featured_count: number;
        recent_services: Service[];
        popular_categories: Array<{ category: ServiceCategory; count: number }>;
      }>(
        SERVICES.LIST,
        { dashboard: true }
      );
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get services with specific ordering
export const useOrderedServices = (ordering: string = '-created_at') => {
  return useQuery({
    queryKey: QUERY_KEYS.SERVICES.LIST({ ordering }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<Service>>(
        SERVICES.LIST,
        { ordering }
      );
      return response.data;
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};
