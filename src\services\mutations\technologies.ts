// TanStack Query mutations for Technologies
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../api/client';
import { TECHNOLOGIES } from '../api/endpoints';
import { QUERY_KEYS } from '../config';
import { handleApiError } from '../utils/errorHandling';
import type {
  Technology,
  TechnologyCategory,
  CreateTechnology,
  UpdateTechnology,
  CreateTechnologyCategory,
  UpdateTechnologyCategory,
} from '../../types/technology';

// Bulk operations types
export interface BulkUpdateTechnologiesRequest {
  ids: number[];
  data: {
    is_active?: boolean;
    is_featured?: boolean;
    category_id?: number;
  };
}

export interface BulkDeleteTechnologiesRequest {
  ids: number[];
}

export interface ReorderTechnologiesRequest {
  technologies: Array<{ id: number; display_order: number }>;
}

// TECHNOLOGY MUTATIONS

// Create new technology
export const useCreateTechnology = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTechnology): Promise<Technology> => {
      const response = await apiClient.post<Technology>(
        TECHNOLOGIES.CREATE,
        data
      );
      return response.data;
    },
    onSuccess: (newTechnology) => {
      // Invalidate and refetch technologies list
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });

      // Invalidate categories to update counts
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
      });

      // Add the new technology to existing cache if possible
      queryClient.setQueryData(
        QUERY_KEYS.TECHNOLOGIES.DETAIL(newTechnology.id),
        newTechnology
      );

      // Invalidate featured technologies if this technology is featured
      if (newTechnology.is_featured) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.TECHNOLOGIES.FEATURED,
        });
      }

      // Invalidate category-specific queries
      if (newTechnology.category_id) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.TECHNOLOGIES.BY_CATEGORY(newTechnology.category_id.toString()),
        });
      }
    },
    onError: (error) => {
      console.error('Failed to create technology:', error);
      throw handleApiError(error);
    },
  });
};

// Update existing technology
export const useUpdateTechnology = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateTechnology): Promise<Technology> => {
      const response = await apiClient.put<Technology>(
        TECHNOLOGIES.UPDATE(data.id),
        data
      );
      return response.data;
    },
    onMutate: async (updatedData) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.DETAIL(updatedData.id),
      });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData<Technology>(
        QUERY_KEYS.TECHNOLOGIES.DETAIL(updatedData.id)
      );

      // Optimistically update the cache
      if (previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.TECHNOLOGIES.DETAIL(updatedData.id),
          { ...previousData, ...updatedData }
        );
      }

      return { previousData };
    },
    onSuccess: (updatedTechnology, variables) => {
      // Update the specific technology in cache
      queryClient.setQueryData(
        QUERY_KEYS.TECHNOLOGIES.DETAIL(updatedTechnology.id),
        updatedTechnology
      );

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });

      // Invalidate featured technologies if featured status changed
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.FEATURED,
      });

      // Invalidate categories if category changed
      if (variables.category_id && variables.category_id !== updatedTechnology.category_id) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
        });
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.TECHNOLOGIES.BY_CATEGORY(variables.category_id.toString()),
        });
      }

      // Invalidate current category
      if (updatedTechnology.category_id) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.TECHNOLOGIES.BY_CATEGORY(updatedTechnology.category_id.toString()),
        });
      }
    },
    onError: (error, updatedData, context) => {
      // Revert optimistic update on error
      if (context?.previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.TECHNOLOGIES.DETAIL(updatedData.id),
          context.previousData
        );
      }
      console.error('Failed to update technology:', error);
      throw handleApiError(error);
    },
  });
};

// Delete technology
export const useDeleteTechnology = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await apiClient.delete(TECHNOLOGIES.DELETE(id));
    },
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.DETAIL(deletedId),
      });

      // Invalidate lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });

      // Invalidate featured technologies
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.FEATURED,
      });

      // Invalidate categories to update counts
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
      });
    },
    onError: (error) => {
      console.error('Failed to delete technology:', error);
      throw handleApiError(error);
    },
  });
};

// Toggle technology featured status
export const useToggleTechnologyFeatured = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, is_featured }: { id: number; is_featured: boolean }): Promise<Technology> => {
      const response = await apiClient.patch<Technology>(
        TECHNOLOGIES.UPDATE(id),
        { is_featured }
      );
      return response.data;
    },
    onMutate: async ({ id, is_featured }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.DETAIL(id),
      });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData<Technology>(
        QUERY_KEYS.TECHNOLOGIES.DETAIL(id)
      );

      // Optimistically update the cache
      if (previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.TECHNOLOGIES.DETAIL(id),
          { ...previousData, is_featured }
        );
      }

      return { previousData };
    },
    onSuccess: (updatedTechnology) => {
      queryClient.setQueryData(
        QUERY_KEYS.TECHNOLOGIES.DETAIL(updatedTechnology.id),
        updatedTechnology
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.FEATURED,
      });
    },
    onError: (error, { id }, context) => {
      // Revert optimistic update on error
      if (context?.previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.TECHNOLOGIES.DETAIL(id),
          context.previousData
        );
      }
      console.error('Failed to toggle technology featured status:', error);
      throw handleApiError(error);
    },
  });
};

// Toggle technology active status
export const useToggleTechnologyActive = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, is_active }: { id: number; is_active: boolean }): Promise<Technology> => {
      const response = await apiClient.patch<Technology>(
        TECHNOLOGIES.UPDATE(id),
        { is_active }
      );
      return response.data;
    },
    onSuccess: (updatedTechnology) => {
      queryClient.setQueryData(
        QUERY_KEYS.TECHNOLOGIES.DETAIL(updatedTechnology.id),
        updatedTechnology
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });
    },
    onError: (error) => {
      console.error('Failed to toggle technology active status:', error);
      throw handleApiError(error);
    },
  });
};

// TECHNOLOGY CATEGORY MUTATIONS

// Create new technology category
export const useCreateTechnologyCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTechnologyCategory): Promise<TechnologyCategory> => {
      const response = await apiClient.post<TechnologyCategory>(
        TECHNOLOGIES.CATEGORIES,
        data
      );
      return response.data;
    },
    onSuccess: (newCategory) => {
      // Invalidate categories list
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
      });
    },
    onError: (error) => {
      console.error('Failed to create technology category:', error);
      throw handleApiError(error);
    },
  });
};

// Update existing technology category
export const useUpdateTechnologyCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateTechnologyCategory): Promise<TechnologyCategory> => {
      const response = await apiClient.put<TechnologyCategory>(
        `${TECHNOLOGIES.CATEGORIES}${data.id}/`,
        data
      );
      return response.data;
    },
    onSuccess: (updatedCategory) => {
      // Invalidate categories list
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
      });

      // Invalidate technologies in this category
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.BY_CATEGORY(updatedCategory.id.toString()),
      });
    },
    onError: (error) => {
      console.error('Failed to update technology category:', error);
      throw handleApiError(error);
    },
  });
};

// Delete technology category
export const useDeleteTechnologyCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await apiClient.delete(`${TECHNOLOGIES.CATEGORIES}${id}/`);
    },
    onSuccess: (_, deletedId) => {
      // Invalidate categories list
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
      });

      // Invalidate technologies in this category
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.BY_CATEGORY(deletedId.toString()),
      });

      // Invalidate all technologies as they might need category updates
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });
    },
    onError: (error) => {
      console.error('Failed to delete technology category:', error);
      throw handleApiError(error);
    },
  });
};

// BULK OPERATIONS

// Bulk update technologies
export const useBulkUpdateTechnologies = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BulkUpdateTechnologiesRequest): Promise<Technology[]> => {
      const response = await apiClient.post<Technology[]>(
        `${TECHNOLOGIES.LIST}bulk-update/`,
        data
      );
      return response.data;
    },
    onSuccess: (updatedTechnologies) => {
      // Update individual items in cache
      updatedTechnologies.forEach((technology) => {
        queryClient.setQueryData(
          QUERY_KEYS.TECHNOLOGIES.DETAIL(technology.id),
          technology
        );
      });

      // Invalidate lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.FEATURED,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
      });
    },
    onError: (error) => {
      console.error('Failed to bulk update technologies:', error);
      throw handleApiError(error);
    },
  });
};

// Bulk delete technologies
export const useBulkDeleteTechnologies = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BulkDeleteTechnologiesRequest): Promise<void> => {
      await apiClient.post(`${TECHNOLOGIES.LIST}bulk-delete/`, data);
    },
    onSuccess: (_, { ids }) => {
      // Remove from cache
      ids.forEach((id) => {
        queryClient.removeQueries({
          queryKey: QUERY_KEYS.TECHNOLOGIES.DETAIL(id),
        });
      });

      // Invalidate lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.FEATURED,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.CATEGORIES,
      });
    },
    onError: (error) => {
      console.error('Failed to bulk delete technologies:', error);
      throw handleApiError(error);
    },
  });
};

// Reorder technologies
export const useReorderTechnologies = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ReorderTechnologiesRequest): Promise<Technology[]> => {
      const response = await apiClient.post<Technology[]>(
        `${TECHNOLOGIES.LIST}reorder/`,
        data
      );
      return response.data;
    },
    onSuccess: (reorderedTechnologies) => {
      // Update individual items in cache
      reorderedTechnologies.forEach((technology) => {
        queryClient.setQueryData(
          QUERY_KEYS.TECHNOLOGIES.DETAIL(technology.id),
          technology
        );
      });

      // Invalidate lists to reflect new order
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.TECHNOLOGIES.ALL,
      });
    },
    onError: (error) => {
      console.error('Failed to reorder technologies:', error);
      throw handleApiError(error);
    },
  });
};
