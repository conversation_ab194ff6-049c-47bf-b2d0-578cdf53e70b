import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { DocSidebar } from './components/DocSidebar';
import { DocContent } from './components/DocContent';
import { DocNavigationItem } from './types';
import { documentationSections } from './config';

export function Documentation() {
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeSection, setActiveSection] = useState<string>('overview');
  const [activeSubsection, setActiveSubsection] = useState<string | undefined>();
  const [docType, setDocType] = useState<'frontend' | 'backend'>('frontend');

  // Initialize from URL params
  useEffect(() => {
    const section = searchParams.get('section');
    const subsection = searchParams.get('subsection');
    const type = searchParams.get('type') as 'frontend' | 'backend';

    if (type && (type === 'frontend' || type === 'backend')) {
      setDocType(type);
    }

    if (section) {
      // Validate that the section exists
      const sectionExists = documentationSections.some(s => s.id === section);
      if (sectionExists) {
        setActiveSection(section);

        if (subsection) {
          // Validate that the subsection exists within the section
          const currentSection = documentationSections.find(s => s.id === section);
          const subsectionExists = currentSection?.subsections?.some(sub => sub.id === subsection);
          if (subsectionExists) {
            setActiveSubsection(subsection);
          } else {
            setActiveSubsection(undefined);
          }
        } else {
          setActiveSubsection(undefined);
        }
      }
    }
  }, [searchParams]);

  const handleNavigate = (item: DocNavigationItem) => {
    setActiveSection(item.section);
    setActiveSubsection(item.subsection);

    // Update URL params
    const newParams = new URLSearchParams();
    newParams.set('section', item.section);
    newParams.set('type', docType);
    if (item.subsection) {
      newParams.set('subsection', item.subsection);
    }
    setSearchParams(newParams);
  };

  const handleDocTypeChange = (type: 'frontend' | 'backend') => {
    setDocType(type);

    // Update URL params
    const newParams = new URLSearchParams();
    newParams.set('section', activeSection);
    newParams.set('type', type);
    if (activeSubsection) {
      newParams.set('subsection', activeSubsection);
    }
    setSearchParams(newParams);
  };

  return (
    <div className="flex h-full bg-background">
      <DocSidebar
        activeSection={activeSection}
        activeSubsection={activeSubsection}
        onNavigate={handleNavigate}
        docType={docType}
        onDocTypeChange={handleDocTypeChange}
      />
      <DocContent
        activeSection={activeSection}
        activeSubsection={activeSubsection}
        docType={docType}
      />
    </div>
  );
}
