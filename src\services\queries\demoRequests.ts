// TanStack Query hooks for Demo Requests
import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { apiClient } from '../api/client';
import { DEMO_REQUESTS } from '../api/endpoints';
import { QUERY_KEYS } from '../config';
import type { PaginatedResponse } from '../api/types';
import type {
  DemoRequest,
  DemoRequestListParams,
  DemoRequestStats,
} from '../../types/demoRequest';

// Get paginated list of demo requests
export const useDemoRequests = (params?: DemoRequestListParams) => {
  return useQuery({
    queryKey: QUERY_KEYS.DEMO_REQUESTS.LIST(params),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<DemoRequest>>(
        DEMO_REQUESTS.LIST,
        params
      );
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes - demo requests change frequently
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get infinite scroll demo requests (for large datasets)
export const useInfiniteDemoRequests = (params?: Omit<DemoRequestListParams, 'page'>) => {
  return useInfiniteQuery({
    queryKey: QUERY_KEYS.DEMO_REQUESTS.LIST({ ...params, infinite: true }),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await apiClient.get<PaginatedResponse<DemoRequest>>(
        DEMO_REQUESTS.LIST,
        { ...params, page: pageParam }
      );
      return response.data;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.next ? allPages.length + 1 : undefined;
    },
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
  });
};

// Get single demo request by ID
export const useDemoRequest = (id: number, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.DEMO_REQUESTS.DETAIL(id),
    queryFn: async () => {
      const response = await apiClient.get<DemoRequest>(
        DEMO_REQUESTS.DETAIL(id)
      );
      return response.data;
    },
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes - individual records are more stable
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Get demo requests statistics
export const useDemoRequestStats = () => {
  return useQuery({
    queryKey: QUERY_KEYS.DEMO_REQUESTS.STATS,
    queryFn: async () => {
      const response = await apiClient.get<DemoRequestStats>(
        DEMO_REQUESTS.STATS
      );
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  });
};

// Get recent demo requests (for dashboard)
export const useRecentDemoRequests = (limit: number = 5) => {
  return useQuery({
    queryKey: QUERY_KEYS.DEMO_REQUESTS.LIST({ recent: true, limit }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<DemoRequest>>(
        DEMO_REQUESTS.LIST,
        { 
          page_size: limit,
          ordering: '-created_at'
        }
      );
      return response.data.results;
    },
    staleTime: 1 * 60 * 1000, // 1 minute - recent data should be fresh
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get demo requests by status
export const useDemoRequestsByStatus = (status: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.DEMO_REQUESTS.LIST({ status }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<DemoRequest>>(
        DEMO_REQUESTS.LIST,
        { status }
      );
      return response.data;
    },
    enabled: !!status && status !== 'all',
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
  });
};

// Get demo requests assigned to specific user
export const useDemoRequestsByAssignee = (assignedTo: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.DEMO_REQUESTS.LIST({ assigned_to: assignedTo }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<DemoRequest>>(
        DEMO_REQUESTS.LIST,
        { assigned_to: assignedTo }
      );
      return response.data;
    },
    enabled: !!assignedTo,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
  });
};

// Search demo requests
export const useSearchDemoRequests = (searchTerm: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.DEMO_REQUESTS.LIST({ search: searchTerm }),
    queryFn: async () => {
      const response = await apiClient.get<PaginatedResponse<DemoRequest>>(
        DEMO_REQUESTS.LIST,
        { search: searchTerm }
      );
      return response.data;
    },
    enabled: enabled && !!searchTerm && searchTerm.length >= 2,
    staleTime: 30 * 1000, // 30 seconds - search results should be fresh
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};
