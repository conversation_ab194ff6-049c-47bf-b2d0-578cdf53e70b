// Main export file for the service layer
// This provides a clean interface for importing service layer functionality

// API Client and core types
export { apiClient, ApiClient } from './api/client';
export type { ApiResponse, PaginatedResponse, ApiError } from './api/client';

// API Types
export type {
  ListQueryParams,
  FilterParams,
  BulkUpdateRequest,
  BulkDeleteRequest,
  FileUploadResponse,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  CreateRequest,
  UpdateRequest,
  DeleteRequest,
  RequestStatus,
  PublicationStatus,
  BaseEntity,
  ActivatableEntity,
  FeaturedEntity,
  OrderedEntity,
  ValidationError,
  ValidationErrorResponse,
  SearchParams,
  PartialExcept,
} from './api/types';

// API Endpoints
export { API_ENDPOINTS, buildQueryString, buildEndpoint } from './api/endpoints';
export {
  AUTH,
  DEMO_REQUESTS,
  SERVICES,
  TECHNOLOGIES,
  TEAM,
  RESOURCES,
  SOCIAL,
  DASHBOARD,
  UPLOADS,
  SYSTEM,
} from './api/endpoints';

// Configuration
export {
  API_CONFIG,
  QUERY_CONFIG,
  QUERY_KEYS,
  createQueryClient,
  DEV_CONFIG,
} from './config';

// Demo Requests service layer
export * from './demoRequests';

// Services service layer
export * from './services';

// Utilities
export {
  ServiceError,
  handleApiError,
  getErrorMessage,
  getValidationErrors,
  isNetworkError,
  isValidationError,
  isAuthError,
  isPermissionError,
  shouldRetry,
  getRetryDelay,
} from './utils';

// Re-export TanStack Query for convenience
export { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
export type {
  UseQueryResult,
  UseMutationResult,
  QueryClient,
  UseInfiniteQueryResult,
} from '@tanstack/react-query';
