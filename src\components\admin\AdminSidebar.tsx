import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  BarChart3,
  Users,
  FileText,
  Settings,
  ChevronDown,
  Home,
  MessageSquare,
  Info,
  Briefcase,
  Code,
  BookOpen,
  Share2,
  Menu,
  Building,
  Palette,
  Book
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

const menuItems = [
  {
    group: "Dashboard",
    items: [
      { title: "Overview", url: "/admin", icon: Home },
      { title: "Analytics", url: "/admin/analytics", icon: BarChart3 },
    ]
  },
  {
    group: "Content Management",
    items: [
      { title: "Demo Requests", url: "/admin/demo-requests", icon: MessageSquare },
      { title: "Team Members", url: "/admin/team", icon: Users },
      { title: "About Us", url: "/admin/about", icon: Info },
      { title: "Services", url: "/admin/services", icon: Briefcase },
      { title: "Technologies", url: "/admin/technologies", icon: Code },
      { title: "Resources", url: "/admin/resources", icon: BookOpen },
    ]
  },
  {
    group: "Settings",
    items: [
      { title: "Social Media", url: "/admin/social", icon: Share2 },
      { title: "User Management", url: "/admin/users", icon: Users },
      { title: "System Settings", url: "/admin/settings", icon: Settings },
    ]
  },
  {
    group: "Development",
    items: [
      { title: "Animation Test", url: "/admin/animation-test", icon: Palette },
      { title: "Documentation", url: "/admin/documentation", icon: Book },
    ]
  }
];

export function AdminSidebar() {
  const { state } = useSidebar();
  const location = useLocation();
  const currentPath = location.pathname;
  const [openGroups, setOpenGroups] = useState<string[]>(["Content Management", "Development"]);
  const isCollapsed = state === "collapsed";

  const isActive = (path: string) => currentPath === path;
  const isGroupActive = (items: { url: string }[]) => 
    items.some(item => currentPath.startsWith(item.url));

  const toggleGroup = (group: string) => {
    setOpenGroups(prev => 
      prev.includes(group) 
        ? prev.filter(g => g !== group)
        : [...prev, group]
    );
  };

  const getNavClassName = (isActive: boolean) => 
    isActive 
      ? "bg-primary/10 text-primary font-medium border-r-2 border-primary" 
      : "hover:bg-accent/50 text-muted-foreground hover:text-foreground";

  return (
    <Sidebar className="border-r border-border" collapsible="icon">
      <SidebarContent className="bg-card">
        <div className="p-4 border-b border-border">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Building className="w-4 h-4 text-primary-foreground" />
            </div>
            {!isCollapsed && (
              <div>
                <h2 className="font-semibold text-sm text-foreground">Admin Dashboard</h2>
                <p className="text-xs text-muted-foreground">Content Management</p>
              </div>
            )}
          </div>
        </div>

        {menuItems.map((section) => {
          const isGroupOpen = openGroups.includes(section.group);
          const hasActiveItem = isGroupActive(section.items);

          return (
            <SidebarGroup key={section.group}>
              <Collapsible open={isGroupOpen} onOpenChange={() => toggleGroup(section.group)}>
                <CollapsibleTrigger asChild>
                  <SidebarGroupLabel className={cn(
                    "flex items-center justify-between cursor-pointer py-2 px-4 hover:bg-accent/50 transition-colors",
                    hasActiveItem && "text-primary"
                  )}>
                    <span className={cn("text-xs font-medium uppercase tracking-wider", isCollapsed && "sr-only")}>
                      {section.group}
                    </span>
                    {!isCollapsed && (
                      <ChevronDown className={cn(
                        "w-4 h-4 transition-transform",
                        isGroupOpen && "rotate-180"
                      )} />
                    )}
                  </SidebarGroupLabel>
                </CollapsibleTrigger>
                
                <CollapsibleContent>
                  <SidebarGroupContent>
                    <SidebarMenu>
                      {section.items.map((item) => {
                        // Define which items should be disabled
                        const disabledItems = [
                          "Analytics",
                          "About Us",
                          // "Services",
                          // "Technologies",
                          // "Resources",
                          // "Social Media",
                          "User Management",
                          "System Settings"
                        ];

                        const isDisabled = disabledItems.includes(item.title);

                        return (
                          <SidebarMenuItem key={item.title}>
                            {isDisabled ? (
                              <SidebarMenuButton
                                disabled
                                className="opacity-50 cursor-not-allowed text-muted-foreground"
                              >
                                <item.icon className="w-4 h-4" />
                                {!isCollapsed && <span>{item.title}</span>}
                              </SidebarMenuButton>
                            ) : (
                              <SidebarMenuButton asChild>
                                <NavLink
                                  to={item.url}
                                  className={getNavClassName(isActive(item.url))}
                                >
                                  <item.icon className="w-4 h-4" />
                                  {!isCollapsed && <span>{item.title}</span>}
                                </NavLink>
                              </SidebarMenuButton>
                            )}
                          </SidebarMenuItem>
                        );
                      })}
                    </SidebarMenu>
                  </SidebarGroupContent>
                </CollapsibleContent>
              </Collapsible>
            </SidebarGroup>
          );
        })}

        <div className="mt-auto p-4 border-t border-border">
          <div className="text-xs text-muted-foreground">
            {!isCollapsed && (
              <>
                <p>Version 1.0.0</p>
                <p>Last updated: {new Date().toLocaleDateString()}</p>
              </>
            )}
          </div>
        </div>
      </SidebarContent>
    </Sidebar>
  );
}