// TypeScript interface matching the SocialMediaLink backend model

export type SocialPlatform = 
  | 'linkedin'
  | 'facebook'
  | 'instagram'
  | 'twitter'
  | 'youtube'
  | 'github'
  | 'dribbble';

export const PLATFORM_CHOICES: Record<SocialPlatform, string> = {
  linkedin: 'LinkedIn',
  facebook: 'Facebook',
  instagram: 'Instagram',
  twitter: 'Twitter',
  youtube: 'YouTube',
  github: 'GitHub',
  dribbble: 'Dribbble',
};

export interface SocialMediaLink {
  id: number;
  platform: SocialPlatform;
  display_name: string;
  url: string;
  icon_class?: string | null;
  icon_svg?: string | null;
  is_active: boolean;
  display_order: number;
  follower_count?: number | null;
  created_at: string;
  updated_at: string;
}

// Type for creating a new social media link (without auto-generated fields)
export interface CreateSocialMediaLink {
  platform: SocialPlatform;
  display_name: string;
  url: string;
  icon_class?: string | null;
  icon_svg?: string | null;
  is_active?: boolean;
  display_order?: number;
  follower_count?: number | null;
}

// Type for updating a social media link
export interface UpdateSocialMediaLink extends Partial<CreateSocialMediaLink> {
  id: number;
}
