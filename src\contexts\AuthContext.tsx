import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiClient } from '@/services/api/client';
import { API_ENDPOINTS } from '@/services/api/endpoints';
import { AuthenticationError } from '@/services/auth';
import type { LoginRequest, LoginResponse } from '@/services/api/types';

interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_superuser: boolean;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  const checkAuth = async () => {
    try {
      const token = apiClient.getAuthToken();
      if (!token) {
        setIsLoading(false);
        return;
      }

      // Handle dev tokens in development mode
      if ((token.startsWith('demo-token-') || token.startsWith('dev-token-')) && import.meta.env.DEV) {
        // TODO: Replace with real API call when backend is ready
        // For now, create a mock user based on token type
        if (token.startsWith('demo-token-')) {
          setUser({
            id: 1,
            email: '<EMAIL>',
            first_name: 'Demo',
            last_name: 'Admin',
            is_staff: true,
            is_superuser: true,
          });
        } else {
          // For dev-token, we don't have the user data stored, so create a generic dev user
          setUser({
            id: 1,
            email: '<EMAIL>',
            first_name: 'Dev',
            last_name: 'User',
            is_staff: true,
            is_superuser: true,
          });
        }
        setIsLoading(false);
        return;
      }

      // TODO: Uncomment when backend API is ready
      // const response = await apiClient.get<User>(API_ENDPOINTS.AUTH.ME);
      // setUser(response.data);

      // For now, if we have any token, assume user is valid (development only)
      if (import.meta.env.DEV) {
        setUser({
          id: 1,
          email: '<EMAIL>',
          first_name: 'Dev',
          last_name: 'User',
          is_staff: true,
          is_superuser: true,
        });
      }
    } catch (error) {
      // Token is invalid, clear it
      apiClient.clearAuthToken();
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginRequest) => {
    try {
      // TODO: Import and use loginUser from auth service when backend is ready
      // For now, use the same development logic as in auth.ts

      // Simulate API delay for realistic UX
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Basic validation - just check if fields are not empty
      if (!credentials.email || !credentials.password) {
        throw new AuthenticationError('Email and password are required');
      }

      if (!credentials.email.includes('@')) {
        throw new AuthenticationError('Please enter a valid email address', 'email');
      }

      if (credentials.password.length < 6) {
        throw new AuthenticationError('Password must be at least 6 characters', 'password');
      }

      // Accept any valid-looking credentials during development
      const mockResponse = {
        access_token: 'dev-token-' + Date.now(),
        refresh_token: 'dev-refresh-token-' + Date.now(),
        user: {
          id: 1,
          email: credentials.email,
          first_name: 'Dev',
          last_name: 'User',
          is_staff: true,
          is_superuser: true,
        },
      };

      const { access_token, user: userData } = mockResponse;

      // Store the token
      apiClient.setAuthToken(access_token);

      // Set user data
      setUser(userData);
    } catch (error) {
      throw error;
    }
  };

  const logout = () => {
    apiClient.clearAuthToken();
    setUser(null);
  };

  useEffect(() => {
    checkAuth();
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
