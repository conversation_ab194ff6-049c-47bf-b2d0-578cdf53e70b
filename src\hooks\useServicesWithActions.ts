// Custom hook that combines services queries and mutations
// This demonstrates how to use the services service layer in components

import { useState, useCallback } from 'react';
import { useToast } from './use-toast';
import {
  useServices,
  useCreateService,
  useUpdateService,
  useDeleteService,
  useToggleServiceFeatured,
  useToggleServiceActive,
  useReorderServices,
  type ServiceListParams,
  type CreateService,
  type UpdateService,
  type ServiceCategory,
  type ReorderServicesRequest,
} from '@/services';

export const useServicesWithActions = (initialParams?: ServiceListParams) => {
  const [params, setParams] = useState<ServiceListParams>(initialParams || {});
  const { toast } = useToast();

  // Queries
  const {
    data: servicesData,
    isLoading,
    isError,
    error,
    refetch,
  } = useServices(params);

  // Mutations
  const createMutation = useCreateService();
  const updateMutation = useUpdateService();
  const deleteMutation = useDeleteService();
  const toggleFeaturedMutation = useToggleServiceFeatured();
  const toggleActiveMutation = useToggleServiceActive();
  const reorderMutation = useReorderServices();

  // Actions
  const createService = useCallback(async (data: CreateService) => {
    try {
      const result = await createMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Service created successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create service",
        variant: "destructive",
      });
      throw error;
    }
  }, [createMutation, toast]);

  const updateService = useCallback(async (data: UpdateService) => {
    try {
      const result = await updateMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Service updated successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update service",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateMutation, toast]);

  const deleteService = useCallback(async (id: number) => {
    try {
      await deleteMutation.mutateAsync(id);
      toast({
        title: "Success",
        description: "Service deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete service",
        variant: "destructive",
      });
      throw error;
    }
  }, [deleteMutation, toast]);

  const toggleFeatured = useCallback(async (id: number, is_featured: boolean) => {
    try {
      const result = await toggleFeaturedMutation.mutateAsync({ id, is_featured });
      toast({
        title: "Success",
        description: `Service ${is_featured ? 'added to' : 'removed from'} featured`,
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update featured status",
        variant: "destructive",
      });
      throw error;
    }
  }, [toggleFeaturedMutation, toast]);

  const toggleActive = useCallback(async (id: number, is_active: boolean) => {
    try {
      const result = await toggleActiveMutation.mutateAsync({ id, is_active });
      toast({
        title: "Success",
        description: `Service ${is_active ? 'activated' : 'deactivated'}`,
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update active status",
        variant: "destructive",
      });
      throw error;
    }
  }, [toggleActiveMutation, toast]);

  const reorderServices = useCallback(async (data: ReorderServicesRequest) => {
    try {
      const result = await reorderMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Services reordered successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reorder services",
        variant: "destructive",
      });
      throw error;
    }
  }, [reorderMutation, toast]);

  // Filter and search functions
  const updateFilters = useCallback((newParams: Partial<ServiceListParams>) => {
    setParams(prev => ({ ...prev, ...newParams, page: 1 })); // Reset to first page
  }, []);

  const clearFilters = useCallback(() => {
    setParams({});
  }, []);

  const setPage = useCallback((page: number) => {
    setParams(prev => ({ ...prev, page }));
  }, []);

  const setCategory = useCallback((category: ServiceCategory | 'all') => {
    setParams(prev => ({ ...prev, category, page: 1 }));
  }, []);

  const setSearch = useCallback((search: string) => {
    setParams(prev => ({ ...prev, search, page: 1 }));
  }, []);

  const setActiveFilter = useCallback((is_active?: boolean) => {
    setParams(prev => ({ ...prev, is_active, page: 1 }));
  }, []);

  const setFeaturedFilter = useCallback((is_featured?: boolean) => {
    setParams(prev => ({ ...prev, is_featured, page: 1 }));
  }, []);

  const setOrdering = useCallback((ordering: string) => {
    setParams(prev => ({ ...prev, ordering, page: 1 }));
  }, []);

  return {
    // Data
    services: servicesData?.results || [],
    totalCount: servicesData?.count || 0,
    hasNextPage: !!servicesData?.next,
    hasPreviousPage: !!servicesData?.previous,
    
    // Loading states
    isLoading,
    isError,
    error,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isTogglingFeatured: toggleFeaturedMutation.isPending,
    isTogglingActive: toggleActiveMutation.isPending,
    isReordering: reorderMutation.isPending,
    
    // Actions
    createService,
    updateService,
    deleteService,
    toggleFeatured,
    toggleActive,
    reorderServices,
    refetch,
    
    // Filters
    params,
    updateFilters,
    clearFilters,
    setPage,
    setCategory,
    setSearch,
    setActiveFilter,
    setFeaturedFilter,
    setOrdering,
  };
};
