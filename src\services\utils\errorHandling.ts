// Error handling utilities for the service layer
import { ApiError } from '../api/types';

// Custom error class for API errors
export class ServiceError extends Error {
  public status: number;
  public details?: Record<string, string[]>;

  constructor(message: string, status: number = 0, details?: Record<string, string[]>) {
    super(message);
    this.name = 'ServiceError';
    this.status = status;
    this.details = details;
  }
}

// Convert API error to ServiceError
export const handleApiError = (error: unknown): ServiceError => {
  if (error instanceof ServiceError) {
    return error;
  }

  if (error && typeof error === 'object' && 'status' in error && 'message' in error) {
    const apiError = error as ApiError;
    return new ServiceError(apiError.message, apiError.status, apiError.details);
  }

  if (error instanceof Error) {
    return new ServiceError(error.message);
  }

  return new ServiceError('An unknown error occurred');
};

// Extract user-friendly error message
export const getErrorMessage = (error: unknown): string => {
  const serviceError = handleApiError(error);
  
  // Handle specific HTTP status codes
  switch (serviceError.status) {
    case 400:
      return 'Invalid request. Please check your input and try again.';
    case 401:
      return 'You are not authorized to perform this action. Please log in.';
    case 403:
      return 'You do not have permission to perform this action.';
    case 404:
      return 'The requested resource was not found.';
    case 409:
      return 'This action conflicts with existing data.';
    case 422:
      return 'The provided data is invalid. Please check your input.';
    case 429:
      return 'Too many requests. Please wait a moment and try again.';
    case 500:
      return 'A server error occurred. Please try again later.';
    case 503:
      return 'The service is temporarily unavailable. Please try again later.';
    default:
      return serviceError.message || 'An unexpected error occurred.';
  }
};

// Extract validation errors for form fields
export const getValidationErrors = (error: unknown): Record<string, string> => {
  const serviceError = handleApiError(error);
  const validationErrors: Record<string, string> = {};

  if (serviceError.details) {
    Object.entries(serviceError.details).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        validationErrors[field] = messages[0];
      }
    });
  }

  return validationErrors;
};

// Check if error is a network error
export const isNetworkError = (error: unknown): boolean => {
  const serviceError = handleApiError(error);
  return serviceError.status === 0 || serviceError.message.toLowerCase().includes('network');
};

// Check if error is a validation error
export const isValidationError = (error: unknown): boolean => {
  const serviceError = handleApiError(error);
  return serviceError.status === 400 || serviceError.status === 422;
};

// Check if error is an authentication error
export const isAuthError = (error: unknown): boolean => {
  const serviceError = handleApiError(error);
  return serviceError.status === 401;
};

// Check if error is a permission error
export const isPermissionError = (error: unknown): boolean => {
  const serviceError = handleApiError(error);
  return serviceError.status === 403;
};

// Retry logic for failed requests
export const shouldRetry = (error: unknown, attemptNumber: number, maxAttempts: number): boolean => {
  if (attemptNumber >= maxAttempts) {
    return false;
  }

  const serviceError = handleApiError(error);
  
  // Don't retry client errors (4xx) except for 408 (timeout) and 429 (rate limit)
  if (serviceError.status >= 400 && serviceError.status < 500) {
    return serviceError.status === 408 || serviceError.status === 429;
  }

  // Retry server errors (5xx) and network errors
  return serviceError.status >= 500 || serviceError.status === 0;
};

// Calculate retry delay with exponential backoff
export const getRetryDelay = (attemptNumber: number, baseDelay: number = 1000): number => {
  return Math.min(baseDelay * Math.pow(2, attemptNumber - 1), 30000);
};
