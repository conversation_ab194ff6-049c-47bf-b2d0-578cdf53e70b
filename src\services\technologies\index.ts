// Technologies service layer exports
// This provides a clean interface for importing technologies functionality

// Query hooks
export {
  useTechnologies,
  useInfiniteTechnologies,
  useTechnology,
  useFeaturedTechnologies,
  useTechnologiesByCategory,
  useActiveTechnologies,
  useTechnologyCategories,
  useTechnologyCategory,
  useActiveTechnologyCategories,
  useSearchTechnologies,
  useTechnologiesForDashboard,
  useOrderedTechnologies,
  useTechnologyStatsByCategory,
} from '../queries/technologies';

export type {
  TechnologyListParams,
  TechnologyCategoryListParams,
} from '../queries/technologies';

// Mutation hooks
export {
  useCreateTechnology,
  useUpdateTechnology,
  useDeleteTechnology,
  useToggleTechnologyFeatured,
  useToggleTechnologyActive,
  useCreateTechnologyCategory,
  useUpdateTechnologyCategory,
  useDeleteTechnologyCategory,
  useBulkUpdateTechnologies,
  useBulkDeleteTechnologies,
  useReorderTechnologies,
} from '../mutations/technologies';

export type {
  BulkUpdateTechnologiesRequest,
  BulkDeleteTechnologiesRequest,
  ReorderTechnologiesRequest,
} from '../mutations/technologies';

// Types (re-export from types/technology.ts)
export type {
  Technology,
  TechnologyCategory,
  CreateTechnology,
  UpdateTechnology,
  CreateTechnologyCategory,
  UpdateTechnologyCategory,
} from '../../types/technology';

// Helper functions
export {
  getCategoryDisplayName,
  getFeaturedTechnologies,
  getTechnologiesByCategory,
  getActiveCategories,
} from '../../types/technology';
