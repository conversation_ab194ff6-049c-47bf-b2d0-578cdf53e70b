import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { createQueryClient, DEV_CONFIG } from "@/services";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import { AdminLayout } from "./components/admin/AdminLayout";
import AdminDashboard from "./pages/admin/AdminDashboard";
import Analytics from "./pages/admin/Analytics";
import DemoRequests from "./pages/admin/DemoRequests";
import TeamMembers from "./pages/admin/TeamMembers";
import About from "./pages/admin/About";
import Services from "./pages/admin/Services";
import Technologies from "./pages/admin/Technologies";
import Resources from "./pages/admin/Resources";
import SocialMedia from "./pages/admin/SocialMedia";
import UserManagement from "./pages/admin/UserManagement";
import AnimationTest from "./pages/admin/AnimationTest";
import Documentation from "./pages/admin/documentation";

const queryClient = createQueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/admin" element={<AdminLayout><AdminDashboard /></AdminLayout>} />
          <Route path="/admin/analytics" element={<AdminLayout><Analytics /></AdminLayout>} />
          <Route path="/admin/demo-requests" element={<AdminLayout><DemoRequests /></AdminLayout>} />
          <Route path="/admin/team" element={<AdminLayout><TeamMembers /></AdminLayout>} />
          <Route path="/admin/about" element={<AdminLayout><About /></AdminLayout>} />
          <Route path="/admin/services" element={<AdminLayout><Services /></AdminLayout>} />
          <Route path="/admin/technologies" element={<AdminLayout><Technologies /></AdminLayout>} />
          <Route path="/admin/resources" element={<AdminLayout><Resources /></AdminLayout>} />
          <Route path="/admin/social" element={<AdminLayout><SocialMedia /></AdminLayout>} />
          <Route path="/admin/users" element={<AdminLayout><UserManagement /></AdminLayout>} />
          <Route path="/admin/settings" element={<AdminLayout><AdminDashboard /></AdminLayout>} />
          <Route path="/admin/animation-test" element={<AdminLayout><AnimationTest /></AdminLayout>} />
          <Route path="/admin/documentation" element={<AdminLayout><Documentation /></AdminLayout>} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
      {DEV_CONFIG.ENABLE_DEVTOOLS && <ReactQueryDevtools initialIsOpen={false} />}
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
