import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { createQueryClient, DEV_CONFIG } from "@/services";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import { AdminLayout } from "./components/admin/AdminLayout";
import AdminDashboard from "./pages/admin/AdminDashboard";
import Analytics from "./pages/admin/Analytics";
import DemoRequests from "./pages/admin/DemoRequests";
import TeamMembers from "./pages/admin/TeamMembers";
import About from "./pages/admin/About";
import Services from "./pages/admin/Services";
import Technologies from "./pages/admin/Technologies";
import Resources from "./pages/admin/Resources";
import SocialMedia from "./pages/admin/SocialMedia";
import UserManagement from "./pages/admin/UserManagement";
import AnimationTest from "./pages/admin/AnimationTest";
import Documentation from "./pages/admin/documentation";

const queryClient = createQueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route
              path="/admin"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><AdminDashboard /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/analytics"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><Analytics /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/demo-requests"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><DemoRequests /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/team"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><TeamMembers /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/about"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><About /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/services"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><Services /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/technologies"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><Technologies /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/resources"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><Resources /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/social"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><SocialMedia /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/users"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><UserManagement /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/settings"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><AdminDashboard /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/animation-test"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><AnimationTest /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/documentation"
              element={
                <ProtectedRoute requireAdmin={true}>
                  <AdminLayout><Documentation /></AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
        {DEV_CONFIG.ENABLE_DEVTOOLS && <ReactQueryDevtools initialIsOpen={false} />}
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
