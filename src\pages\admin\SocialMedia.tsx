import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Save, Plus, Edit, Trash2, ExternalLink,
  Facebook, Twitter, Instagram, Linkedin,
  Github, Youtube, Globe, Mail, Dribbble
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { SocialMediaLink, CreateSocialMediaLink, PLATFORM_CHOICES, SocialPlatform } from "@/types/social";

export default function SocialMedia() {
  const { toast } = useToast();
  const [isAddingLink, setIsAddingLink] = useState(false);
  const [editingLink, setEditing<PERSON><PERSON>] = useState<SocialMediaLink | null>(null);
  const [deletingLink, setDeletingLink] = useState<SocialMediaLink | null>(null);

  const [socialLinks, setSocialLinks] = useState<SocialMediaLink[]>([
    {
      id: 1,
      platform: "linkedin",
      display_name: "TechFlow Solutions",
      url: "https://linkedin.com/company/techflow-solutions",
      follower_count: 1250,
      is_active: true,
      display_order: 1,
      icon_class: "fab fa-linkedin",
      icon_svg: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 2,
      platform: "github",
      display_name: "TechFlow Solutions",
      url: "https://github.com/techflow-solutions",
      follower_count: 890,
      is_active: true,
      display_order: 2,
      icon_class: "fab fa-github",
      icon_svg: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 3,
      platform: "twitter",
      display_name: "TechFlow Solutions",
      url: "https://twitter.com/techflow_dev",
      follower_count: 2100,
      is_active: true,
      display_order: 3,
      icon_class: "fab fa-twitter",
      icon_svg: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 4,
      platform: "youtube",
      display_name: "TechFlow Solutions",
      url: "https://youtube.com/@techflow-solutions",
      follower_count: 450,
      is_active: false,
      display_order: 4,
      icon_class: "fab fa-youtube",
      icon_svg: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ]);

  const platformOptions = [
    { platform: "linkedin", name: "LinkedIn", icon: Linkedin, color: "text-blue-600" },
    { platform: "github", name: "GitHub", icon: Github, color: "text-gray-900" },
    { platform: "twitter", name: "Twitter", icon: Twitter, color: "text-blue-400" },
    { platform: "facebook", name: "Facebook", icon: Facebook, color: "text-blue-600" },
    { platform: "instagram", name: "Instagram", icon: Instagram, color: "text-pink-600" },
    { platform: "youtube", name: "YouTube", icon: Youtube, color: "text-red-600" },
    { platform: "dribbble", name: "Dribbble", icon: Dribbble, color: "text-pink-500" }
  ];

  const handleSaveLink = (link: Partial<CreateSocialMediaLink>) => {
    if (editingLink) {
      const updatedLink = {
        ...editingLink,
        ...link,
        updated_at: new Date().toISOString()
      };
      setSocialLinks(prev => prev.map(l => l.id === editingLink.id ? updatedLink : l));
      toast({ title: "Social Link Updated", description: "Social media link has been successfully updated." });
    } else {
      const newLink: SocialMediaLink = {
        id: Date.now(),
        platform: link.platform || "linkedin",
        display_name: link.display_name || "",
        url: link.url || "",
        icon_class: link.icon_class || null,
        icon_svg: link.icon_svg || null,
        follower_count: link.follower_count || null,
        is_active: link.is_active ?? true,
        display_order: link.display_order || socialLinks.length + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setSocialLinks(prev => [...prev, newLink]);
      toast({ title: "Social Link Added", description: "New social media link has been successfully added." });
    }
    setIsAddingLink(false);
    setEditingLink(null);
  };

  const deleteLink = (id: number) => {
    setSocialLinks(prev => prev.filter(l => l.id !== id));
    setDeletingLink(null);
    toast({ title: "Social Link Deleted", description: "Social media link has been removed." });
  };

  const confirmDelete = (link: SocialMediaLink) => {
    setDeletingLink(link);
  };

  const updateFollowers = () => {
    toast({ 
      title: "Followers Updated", 
      description: "Follower counts have been refreshed from external APIs." 
    });
  };

  const SocialLinkForm = ({ link }: { link?: SocialMediaLink }) => {
    const [formData, setFormData] = useState({
      platform: link?.platform || ("linkedin" as SocialPlatform),
      display_name: link?.display_name || "",
      url: link?.url || "",
      icon_class: link?.icon_class || "",
      icon_svg: link?.icon_svg || "",
      follower_count: link?.follower_count || null,
      is_active: link?.is_active ?? true,
    });

    const [errors, setErrors] = useState<Record<string, string>>({});

    const validateForm = () => {
      const newErrors: Record<string, string> = {};

      if (!formData.platform) {
        newErrors.platform = "Platform is required";
      }
      if (!formData.display_name.trim()) {
        newErrors.display_name = "Display name is required";
      }
      if (!formData.url.trim()) {
        newErrors.url = "URL is required";
      } else if (!/^https?:\/\/.+/.test(formData.url)) {
        newErrors.url = "Please enter a valid URL starting with http:// or https://";
      }

      // Check for duplicate platform (only for new links or different platform)
      const existingLink = socialLinks.find(l => l.platform === formData.platform && l.id !== link?.id);
      if (existingLink) {
        newErrors.platform = "A link for this platform already exists";
      }

      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = () => {
      if (validateForm()) {
        handleSaveLink(formData);
      }
    };

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="platform">Platform *</Label>
            <select
              id="platform"
              value={formData.platform}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, platform: e.target.value as SocialPlatform }));
                if (errors.platform) setErrors(prev => ({ ...prev, platform: "" }));
              }}
              className={`w-full p-2 border rounded-md ${errors.platform ? 'border-red-500' : ''}`}
            >
              <option value="">Select Platform</option>
              {platformOptions.map(platform => (
                <option key={platform.platform} value={platform.platform}>{platform.name}</option>
              ))}
            </select>
            {errors.platform && <p className="text-red-500 text-sm mt-1">{errors.platform}</p>}
          </div>
          <div>
            <Label htmlFor="display_name">Display Name *</Label>
            <Input
              id="display_name"
              value={formData.display_name}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, display_name: e.target.value }));
                if (errors.display_name) setErrors(prev => ({ ...prev, display_name: "" }));
              }}
              placeholder="e.g., TechFlow Solutions"
              className={errors.display_name ? 'border-red-500' : ''}
            />
            {errors.display_name && <p className="text-red-500 text-sm mt-1">{errors.display_name}</p>}
          </div>
        </div>

        <div>
          <Label htmlFor="url">URL *</Label>
          <Input
            id="url"
            value={formData.url}
            onChange={(e) => {
              setFormData(prev => ({ ...prev, url: e.target.value }));
              if (errors.url) setErrors(prev => ({ ...prev, url: "" }));
            }}
            placeholder="https://platform.com/username"
            className={errors.url ? 'border-red-500' : ''}
          />
          {errors.url && <p className="text-red-500 text-sm mt-1">{errors.url}</p>}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="icon_class">Icon Class (Optional)</Label>
            <Input
              id="icon_class"
              value={formData.icon_class || ""}
              onChange={(e) => setFormData(prev => ({ ...prev, icon_class: e.target.value || null }))}
              placeholder="e.g., fab fa-linkedin"
            />
          </div>
          <div>
            <Label htmlFor="follower_count">Followers Count (Optional)</Label>
            <Input
              id="follower_count"
              type="number"
              value={formData.follower_count || ""}
              onChange={(e) => setFormData(prev => ({ ...prev, follower_count: e.target.value ? parseInt(e.target.value) : null }))}
              placeholder="0"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="icon_svg">Custom SVG Icon (Optional)</Label>
          <textarea
            id="icon_svg"
            value={formData.icon_svg || ""}
            onChange={(e) => setFormData(prev => ({ ...prev, icon_svg: e.target.value || null }))}
            placeholder="<svg>...</svg>"
            className="w-full p-2 border rounded-md min-h-[80px]"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="is_active"
            checked={formData.is_active}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
          />
          <Label htmlFor="is_active">Active (Display on website)</Label>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => { setIsAddingLink(false); setEditingLink(null); }}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            {link ? "Update" : "Add"} Link
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Social Media Management</h1>
        <div className="flex gap-2">
          {/* <Button variant="outline" onClick={updateFollowers}>
            Refresh Followers
          </Button> */}
          <Button onClick={() => setIsAddingLink(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Social Link
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {socialLinks.filter(link => link.is_active).length}
              </div>
              <div className="text-sm text-muted-foreground">Active Platforms</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {socialLinks.reduce((sum, link) => sum + (link.follower_count || 0), 0).toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Total Followers</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {Math.max(...socialLinks.map(link => link.follower_count || 0)).toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Largest Following</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {Math.round(socialLinks.reduce((sum, link) => sum + (link.follower_count || 0), 0) / socialLinks.length).toLocaleString()}
              </div>
              <div className="text-sm text-muted-foreground">Average Followers</div>
            </div>
          </CardContent>
        </Card>
      </div> */}

      {/* Social Links Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {socialLinks.map((link) => {
          const platformOption = platformOptions.find(p => p.platform === link.platform);
          const Icon = platformOption?.icon || Globe;
          const color = platformOption?.color || "text-gray-600";

          return (
            <Card key={link.id} className={`relative ${!link.is_active ? "opacity-60" : ""}`}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg bg-accent ${color}`}>
                      {link.icon_svg ? (
                        <div dangerouslySetInnerHTML={{ __html: link.icon_svg }} className="w-6 h-6" />
                      ) : (
                        <Icon className="w-6 h-6" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold">{PLATFORM_CHOICES[link.platform]}</h3>
                      <p className="text-sm text-muted-foreground">{link.display_name}</p>
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <Button size="sm" variant="outline" asChild>
                      <a href={link.url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => setEditingLink(link)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => confirmDelete(link)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <p className="font-medium">{link.display_name}</p>
                    <p className="text-sm text-muted-foreground truncate">{link.url}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    {/* <div className="text-center">
                      <div className="text-lg font-bold">{(link.follower_count || 0).toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">Followers</div>
                    </div> */}
                    <div className="flex space-x-2">
                      <Badge variant={link.is_active ? "secondary" : "outline"}>
                        {link.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>

                  <div className="pt-3 border-t">
                    <div className="text-xs text-muted-foreground">
                      Display Order: #{link.display_order}
                    </div>
                    {link.icon_class && (
                      <div className="text-xs text-muted-foreground">
                        Icon Class: {link.icon_class}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Website Integration Preview */}
      {/* <Card>
        <CardHeader>
          <CardTitle>Website Integration Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-6 bg-accent/20 rounded-lg">
            <h3 className="text-lg font-semibold mb-4 text-center">Follow Us</h3>
            <div className="flex justify-center space-x-4">
              {socialLinks
                .filter(link => link.is_active)
                .sort((a, b) => a.display_order - b.display_order)
                .map((link) => {
                  const platformOption = platformOptions.find(p => p.platform === link.platform);
                  const Icon = platformOption?.icon || Globe;
                  const color = platformOption?.color || "text-gray-600";

                  return (
                    <a
                      key={link.id}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`p-3 rounded-full bg-background shadow-md hover:shadow-lg transition-shadow ${color}`}
                      title={`${link.display_name} - ${(link.follower_count || 0).toLocaleString()} followers`}
                    >
                      {link.icon_svg ? (
                        <div dangerouslySetInnerHTML={{ __html: link.icon_svg }} className="w-5 h-5" />
                      ) : (
                        <Icon className="w-5 h-5" />
                      )}
                    </a>
                  );
                })}
            </div>
          </div>
        </CardContent>
      </Card> */}

      {/* Add Social Link Dialog */}
      <Dialog open={isAddingLink} onOpenChange={setIsAddingLink}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Social Media Link</DialogTitle>
          </DialogHeader>
          <SocialLinkForm />
        </DialogContent>
      </Dialog>

      {/* Edit Social Link Dialog */}
      <Dialog open={!!editingLink} onOpenChange={() => setEditingLink(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Social Media Link</DialogTitle>
          </DialogHeader>
          {editingLink && <SocialLinkForm link={editingLink} />}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!deletingLink} onOpenChange={() => setDeletingLink(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Social Media Link</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Are you sure you want to delete the {deletingLink && PLATFORM_CHOICES[deletingLink.platform]} link for "{deletingLink?.display_name}"?</p>
            <p className="text-sm text-muted-foreground">This action cannot be undone.</p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setDeletingLink(null)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={() => deletingLink && deleteLink(deletingLink.id)}>
                Delete
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}