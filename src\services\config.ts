// Configuration for API services and TanStack Query
import { QueryClient } from '@tanstack/react-query';

// Environment configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  RETRY_ATTEMPTS: parseInt(import.meta.env.VITE_API_RETRY_ATTEMPTS || '3'),
  RETRY_DELAY: parseInt(import.meta.env.VITE_API_RETRY_DELAY || '1000'),
} as const;

// TanStack Query configuration
export const QUERY_CONFIG = {
  // Default stale time (5 minutes)
  STALE_TIME: 5 * 60 * 1000,
  
  // Default cache time (10 minutes)
  CACHE_TIME: 10 * 60 * 1000,
  
  // Retry configuration
  RETRY: 3,
  RETRY_DELAY: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
  
  // Refetch configuration
  REFETCH_ON_WINDOW_FOCUS: false,
  REFETCH_ON_RECONNECT: true,
  REFETCH_ON_MOUNT: true,
} as const;

// Query keys for consistent caching
export const QUERY_KEYS = {
  // Demo Requests
  DEMO_REQUESTS: {
    ALL: ['demo-requests'] as const,
    LIST: (params?: any) => ['demo-requests', 'list', params] as const,
    DETAIL: (id: number) => ['demo-requests', 'detail', id] as const,
    STATS: ['demo-requests', 'stats'] as const,
  },

  // Services
  SERVICES: {
    ALL: ['services'] as const,
    LIST: (params?: any) => ['services', 'list', params] as const,
    DETAIL: (id: number) => ['services', 'detail', id] as const,
    FEATURED: ['services', 'featured'] as const,
    CATEGORIES: ['services', 'categories'] as const,
  },

  // Technologies
  TECHNOLOGIES: {
    ALL: ['technologies'] as const,
    LIST: (params?: any) => ['technologies', 'list', params] as const,
    DETAIL: (id: number) => ['technologies', 'detail', id] as const,
    CATEGORIES: ['technologies', 'categories'] as const,
    FEATURED: ['technologies', 'featured'] as const,
    BY_CATEGORY: (category: string) => ['technologies', 'category', category] as const,
  },

  // Team Members
  TEAM: {
    ALL: ['team'] as const,
    LIST: (params?: any) => ['team', 'list', params] as const,
    DETAIL: (id: number) => ['team', 'detail', id] as const,
    ACTIVE: ['team', 'active'] as const,
  },

  // Resources
  RESOURCES: {
    ALL: ['resources'] as const,
    LIST: (params?: any) => ['resources', 'list', params] as const,
    DETAIL: (id: number) => ['resources', 'detail', id] as const,
    CATEGORIES: ['resources', 'categories'] as const,
    PUBLISHED: ['resources', 'published'] as const,
    BY_CATEGORY: (category: string) => ['resources', 'category', category] as const,
  },

  // Social Media
  SOCIAL: {
    ALL: ['social'] as const,
    LIST: (params?: any) => ['social', 'list', params] as const,
    DETAIL: (id: number) => ['social', 'detail', id] as const,
    ACTIVE: ['social', 'active'] as const,
  },

  // Dashboard
  DASHBOARD: {
    ALL: ['dashboard'] as const,
    OVERVIEW: ['dashboard', 'overview'] as const,
    METRICS: ['dashboard', 'metrics'] as const,
    RECENT_ACTIVITIES: ['dashboard', 'recent-activities'] as const,
    ANALYTICS: ['dashboard', 'analytics'] as const,
    STATS: {
      DEMO_REQUESTS: ['dashboard', 'stats', 'demo-requests'] as const,
      SERVICES: ['dashboard', 'stats', 'services'] as const,
      TECHNOLOGIES: ['dashboard', 'stats', 'technologies'] as const,
      TEAM: ['dashboard', 'stats', 'team'] as const,
      RESOURCES: ['dashboard', 'stats', 'resources'] as const,
    },
  },

  // Authentication
  AUTH: {
    ME: ['auth', 'me'] as const,
  },
} as const;

// Create and configure QueryClient
export const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: QUERY_CONFIG.STALE_TIME,
        gcTime: QUERY_CONFIG.CACHE_TIME, // Updated from cacheTime to gcTime
        retry: QUERY_CONFIG.RETRY,
        retryDelay: QUERY_CONFIG.RETRY_DELAY,
        refetchOnWindowFocus: QUERY_CONFIG.REFETCH_ON_WINDOW_FOCUS,
        refetchOnReconnect: QUERY_CONFIG.REFETCH_ON_RECONNECT,
        refetchOnMount: QUERY_CONFIG.REFETCH_ON_MOUNT,
      },
      mutations: {
        retry: 1,
        retryDelay: 1000,
      },
    },
  });
};

// Development mode configuration
export const DEV_CONFIG = {
  ENABLE_DEVTOOLS: import.meta.env.DEV,
  LOG_QUERIES: import.meta.env.DEV,
  MOCK_API: import.meta.env.VITE_MOCK_API === 'true',
} as const;
