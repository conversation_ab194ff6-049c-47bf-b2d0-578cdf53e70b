import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, Settings, Users, BarChart3, Sparkles, Zap, Star, Circle } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { LoginModal } from "@/components/auth/LoginModal";
import { useAuth } from "@/contexts/AuthContext";

const Index = () => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  // Floating animation variants
  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      rotate: [0, 5, -5, 0],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const handleAccessDashboard = () => {
    if (isAuthenticated) {
      navigate('/admin');
    } else {
      setIsLoginModalOpen(true);
    }
  };

  const handleLoginSuccess = () => {
    navigate('/admin');
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const staggerItem = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Floating Circles */}
        <motion.div
          className="absolute top-20 left-10 w-32 h-32 bg-primary/5 rounded-full"
          animate={{
            y: [-20, 20, -20],
            x: [-10, 10, -10],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-40 right-20 w-24 h-24 bg-secondary/10 rounded-full"
          animate={{
            y: [20, -20, 20],
            x: [10, -10, 10],
            scale: [1.1, 1, 1.1],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />
        <motion.div
          className="absolute bottom-40 left-1/4 w-16 h-16 bg-accent/15 rounded-full"
          animate={{
            y: [-15, 15, -15],
            rotate: [0, 360],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        {/* Floating Icons */}
        <motion.div
          className="absolute top-32 right-1/4 text-primary/20"
          variants={floatingVariants}
          animate="animate"
        >
          <Sparkles className="w-8 h-8" />
        </motion.div>
        <motion.div
          className="absolute bottom-32 right-10 text-secondary/20"
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 2 }}
        >
          <Zap className="w-6 h-6" />
        </motion.div>
        <motion.div
          className="absolute top-1/2 left-20 text-accent/20"
          variants={floatingVariants}
          animate="animate"
          transition={{ delay: 4 }}
        >
          <Star className="w-7 h-7" />
        </motion.div>
      </div>

      <div className="container mx-auto px-6 py-16 relative z-10">
        {/* Development Demo Status */}
        {import.meta.env.DEV && isAuthenticated && user?.email === '<EMAIL>' && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-md mx-auto mb-8 p-3 bg-orange-100 border border-orange-200 rounded-lg text-center"
          >
            <p className="text-sm text-orange-800">
              🚀 <strong>Demo Mode Active</strong> - Logged in as {user.first_name} {user.last_name}
            </p>
          </motion.div>
        )}

        <motion.div
          className="max-w-4xl mx-auto text-center space-y-8"
          variants={staggerContainer}
          initial="hidden"
          animate="show"
        >
          {/* Header */}
          <motion.div className="space-y-4" variants={staggerItem}>
            <motion.div
              className="w-16 h-16 mx-auto bg-primary rounded-xl flex items-center justify-center"
              whileHover={{
                scale: 1.1,
                rotate: 5,
                boxShadow: "0 10px 25px rgba(0,0,0,0.1)"
              }}
              whileTap={{ scale: 0.95 }}
            >
              <Building className="w-8 h-8 text-primary-foreground" />
            </motion.div>
            <motion.h1
              className="text-5xl font-bold text-foreground"
              variants={staggerItem}
            >
              Software Development Company
            </motion.h1>
            <motion.p
              className="text-xl text-muted-foreground max-w-2xl mx-auto"
              variants={staggerItem}
            >
              Professional admin dashboard for managing your company's website content, team members, services, and client interactions.
            </motion.p>
          </motion.div>

          {/* Quick Access Cards */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12"
            variants={staggerContainer}
          >
            <motion.div variants={staggerItem}>
              <motion.div
                whileHover={{
                  scale: 1.05,
                  y: -5,
                  boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
                }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <Card className="hover:shadow-lg transition-shadow cursor-pointer group border-0 bg-card/50 backdrop-blur-sm">
                  <Link to="/admin">
                    <CardHeader className="text-center pb-4">
                      <motion.div
                        className="w-12 h-12 mx-auto bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                      >
                        <BarChart3 className="w-6 h-6 text-primary" />
                      </motion.div>
                      <CardTitle>Dashboard</CardTitle>
                      <CardDescription>
                        View analytics, metrics, and manage your business overview
                      </CardDescription>
                    </CardHeader>
                  </Link>
                </Card>
              </motion.div>
            </motion.div>

            <motion.div variants={staggerItem}>
              <motion.div
                whileHover={{
                  scale: 1.05,
                  y: -5,
                  boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
                }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <Card className="hover:shadow-lg transition-shadow cursor-pointer group border-0 bg-card/50 backdrop-blur-sm">
                  <Link to="/admin/team">
                    <CardHeader className="text-center pb-4">
                      <motion.div
                        className="w-12 h-12 mx-auto bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                      >
                        <Users className="w-6 h-6 text-primary" />
                      </motion.div>
                      <CardTitle>Team Management</CardTitle>
                      <CardDescription>
                        Manage team member profiles, roles, and information
                      </CardDescription>
                    </CardHeader>
                  </Link>
                </Card>
              </motion.div>
            </motion.div>

            <motion.div variants={staggerItem}>
              <motion.div
                whileHover={{
                  scale: 1.05,
                  y: -5,
                  boxShadow: "0 20px 40px rgba(0,0,0,0.1)"
                }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                <Card className="hover:shadow-lg transition-shadow cursor-pointer group border-0 bg-card/50 backdrop-blur-sm">
                  <Link to="/admin/demo-requests">
                    <CardHeader className="text-center pb-4">
                      <motion.div
                        className="w-12 h-12 mx-auto bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors"
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.6 }}
                      >
                        <Settings className="w-6 h-6 text-primary" />
                      </motion.div>
                      <CardTitle>Content Management</CardTitle>
                      <CardDescription>
                        Handle demo requests, services, and website content
                      </CardDescription>
                    </CardHeader>
                  </Link>
                </Card>
              </motion.div>
            </motion.div>
          </motion.div>

          {/* Main CTA */}
          <motion.div className="pt-8" variants={staggerItem}>
            <Button
              onClick={handleAccessDashboard}
              size="lg"
              className="text-lg px-8 py-6 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
            >
              {isAuthenticated ? 'Go to Dashboard' : 'Access Admin Dashboard'}
            </Button>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-16 text-left"
            variants={staggerContainer}
          >
            <motion.div
              className="space-y-3 p-6 rounded-xl bg-card/30 backdrop-blur-sm border border-border/50"
              variants={staggerItem}
              whileHover={{
                scale: 1.02,
                backgroundColor: "rgba(255,255,255,0.1)",
                transition: { duration: 0.2 }
              }}
            >
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <Settings className="w-5 h-5 text-primary" />
                </motion.div>
                Content Management
              </h3>
              <motion.ul className="space-y-1 text-muted-foreground">
                {[
                  "Demo request tracking and scheduling",
                  "Team member profile management",
                  "Service portfolio organization",
                  "Technology showcase administration"
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 + 0.5 }}
                  >
                    • {item}
                  </motion.li>
                ))}
              </motion.ul>
            </motion.div>
            <motion.div
              className="space-y-3 p-6 rounded-xl bg-card/30 backdrop-blur-sm border border-border/50"
              variants={staggerItem}
              whileHover={{
                scale: 1.02,
                backgroundColor: "rgba(255,255,255,0.1)",
                transition: { duration: 0.2 }
              }}
            >
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                >
                  <BarChart3 className="w-5 h-5 text-primary" />
                </motion.div>
                Dashboard Features
              </h3>
              <motion.ul className="space-y-1 text-muted-foreground">
                {[
                  "Real-time analytics and metrics",
                  "Responsive design for all devices",
                  "Intuitive navigation and search",
                  "Role-based access control"
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 + 0.7 }}
                  >
                    • {item}
                  </motion.li>
                ))}
              </motion.ul>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        onSuccess={handleLoginSuccess}
      />
    </div>
  );
};

export default Index;