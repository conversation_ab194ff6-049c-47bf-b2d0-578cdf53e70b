// Services service layer exports
// This provides a clean interface for importing services functionality

// Query hooks
export {
  useServices,
  useInfiniteServices,
  useService,
  useFeaturedServices,
  useServicesByCategory,
  useActiveServices,
  useServiceCategories,
  useSearchServices,
  useServicesForDashboard,
  useOrderedServices,
} from '../queries/services';

export type { ServiceListParams } from '../queries/services';

// Mutation hooks
export {
  useCreateService,
  useUpdateService,
  useDeleteService,
  useToggleServiceFeatured,
  useToggleServiceActive,
  useBulkUpdateServices,
  useBulkDeleteServices,
  useReorderServices,
} from '../mutations/services';

export type {
  BulkUpdateServicesRequest,
  BulkDeleteServicesRequest,
  ReorderServicesRequest,
} from '../mutations/services';

// Types (re-export from types/service.ts)
export type {
  Service,
  CreateService,
  UpdateService,
  ServiceCategory,
} from '../../types/service';

// Helper functions
export {
  getCategoryDisplayName,
  SERVICE_CATEGORY_CHOICES,
} from '../../types/service';
