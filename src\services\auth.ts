// Authentication service functions
import { apiClient } from './api/client';
import { API_ENDPOINTS } from './api/endpoints';
import type { LoginRequest, LoginResponse } from './api/types';

export interface AuthError {
  message: string;
  field?: string;
}

export class AuthenticationError extends Error {
  public field?: string;
  
  constructor(message: string, field?: string) {
    super(message);
    this.name = 'AuthenticationError';
    this.field = field;
  }
}

/**
 * Demo credentials for development
 */
const DEMO_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'demo123456'
};

/**
 * Mock demo user data
 */
const DEMO_USER = {
  id: 1,
  email: '<EMAIL>',
  first_name: 'Demo',
  last_name: 'Admin',
  is_staff: true,
  is_superuser: true,
};

/**
 * Check if we're in development mode and should use demo login
 */
const isDemoMode = (): boolean => {
  return import.meta.env.DEV || import.meta.env.VITE_DEMO_MODE === 'true';
};

/**
 * Handle demo login for development
 */
const handleDemoLogin = (credentials: LoginRequest): LoginResponse => {
  if (credentials.email === DEMO_CREDENTIALS.email &&
      credentials.password === DEMO_CREDENTIALS.password) {
    return {
      access_token: 'demo-token-' + Date.now(),
      refresh_token: 'demo-refresh-token-' + Date.now(),
      user: DEMO_USER,
    };
  }
  throw new AuthenticationError('Invalid demo credentials');
};

/**
 * Authenticate user with email/username and password
 */
export const loginUser = async (credentials: LoginRequest): Promise<LoginResponse> => {
  // TODO: Integrate with real Django REST API when backend is ready
  // For now, accept any login during development

  // Simulate API delay for realistic UX
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Basic validation - just check if fields are not empty
  if (!credentials.email || !credentials.password) {
    throw new AuthenticationError('Email and password are required');
  }

  if (!credentials.email.includes('@')) {
    throw new AuthenticationError('Please enter a valid email address', 'email');
  }

  if (credentials.password.length < 6) {
    throw new AuthenticationError('Password must be at least 6 characters', 'password');
  }

  // Accept any valid-looking credentials during development
  return {
    access_token: 'dev-token-' + Date.now(),
    refresh_token: 'dev-refresh-token-' + Date.now(),
    user: {
      id: 1,
      email: credentials.email,
      first_name: 'Dev',
      last_name: 'User',
      is_staff: true,
      is_superuser: true,
    },
  };

  /* TODO: Replace above with real API call when backend is ready:

  try {
    const response = await apiClient.post<LoginResponse>(
      API_ENDPOINTS.AUTH.LOGIN,
      credentials
    );

    return response.data;
  } catch (error: any) {
    // Handle different types of authentication errors
    if (error.status === 401) {
      throw new AuthenticationError('Invalid email or password');
    } else if (error.status === 400) {
      // Handle validation errors
      const details = error.details;
      if (details?.email) {
        throw new AuthenticationError(details.email[0], 'email');
      } else if (details?.password) {
        throw new AuthenticationError(details.password[0], 'password');
      } else {
        throw new AuthenticationError('Please check your credentials');
      }
    } else if (error.status >= 500) {
      throw new AuthenticationError('Server error. Please try again later.');
    } else {
      throw new AuthenticationError('Login failed. Please try again.');
    }
  }
  */
};

/**
 * Get current user information
 */
export const getCurrentUser = async () => {
  try {
    const response = await apiClient.get(API_ENDPOINTS.AUTH.ME);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Logout user
 */
export const logoutUser = async (): Promise<void> => {
  try {
    await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT);
  } catch (error) {
    // Even if logout fails on server, we should clear local token
    console.warn('Logout request failed:', error);
  } finally {
    // Always clear local token
    apiClient.clearAuthToken();
  }
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = apiClient.getAuthToken();
  return !!token;
};

/**
 * Get stored auth token
 */
export const getAuthToken = (): string | null => {
  return apiClient.getAuthToken();
};

/**
 * Clear authentication data
 */
export const clearAuth = (): void => {
  apiClient.clearAuthToken();
};
