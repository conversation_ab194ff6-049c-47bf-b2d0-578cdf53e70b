// API endpoint constants for Django REST Framework
// This file centralizes all API endpoints for easy maintenance and consistency

// Base API paths
export const API_ENDPOINTS = {
  // Authentication endpoints
  AUTH: {
    LOGIN: '/auth/login/',
    LOGOUT: '/auth/logout/',
    REFRESH: '/auth/refresh/',
    ME: '/auth/me/',
    CHANGE_PASSWORD: '/auth/change-password/',
  },

  // Demo Requests endpoints
  DEMO_REQUESTS: {
    LIST: '/demo-requests/',
    DETAIL: (id: number) => `/demo-requests/${id}/`,
    CREATE: '/demo-requests/',
    UPDATE: (id: number) => `/demo-requests/${id}/`,
    DELETE: (id: number) => `/demo-requests/${id}/`,
    BULK_UPDATE: '/demo-requests/bulk-update/',
    BULK_DELETE: '/demo-requests/bulk-delete/',
    EXPORT: '/demo-requests/export/',
    STATS: '/demo-requests/stats/',
  },

  // Services endpoints
  SERVICES: {
    LIST: '/services/',
    DETAIL: (id: number) => `/services/${id}/`,
    CREATE: '/services/',
    UPDATE: (id: number) => `/services/${id}/`,
    DELETE: (id: number) => `/services/${id}/`,
    FEATURED: '/services/featured/',
    REORDER: '/services/reorder/',
    CATEGORIES: '/services/categories/',
  },

  // Technologies endpoints
  TECHNOLOGIES: {
    LIST: '/technologies/',
    DETAIL: (id: number) => `/technologies/${id}/`,
    CREATE: '/technologies/',
    UPDATE: (id: number) => `/technologies/${id}/`,
    DELETE: (id: number) => `/technologies/${id}/`,
    CATEGORIES: '/technologies/categories/',
    FEATURED: '/technologies/featured/',
    BY_CATEGORY: (category: string) => `/technologies/category/${category}/`,
  },

  // Team Members endpoints
  TEAM: {
    LIST: '/team/',
    DETAIL: (id: number) => `/team/${id}/`,
    CREATE: '/team/',
    UPDATE: (id: number) => `/team/${id}/`,
    DELETE: (id: number) => `/team/${id}/`,
    REORDER: '/team/reorder/',
    ACTIVE: '/team/active/',
  },

  // Resources endpoints
  RESOURCES: {
    LIST: '/resources/',
    DETAIL: (id: number) => `/resources/${id}/`,
    CREATE: '/resources/',
    UPDATE: (id: number) => `/resources/${id}/`,
    DELETE: (id: number) => `/resources/${id}/`,
    CATEGORIES: '/resources/categories/',
    PUBLISHED: '/resources/published/',
    BY_CATEGORY: (category: string) => `/resources/category/${category}/`,
  },

  // Social Media endpoints
  SOCIAL: {
    LIST: '/social-media/',
    DETAIL: (id: number) => `/social-media/${id}/`,
    CREATE: '/social-media/',
    UPDATE: (id: number) => `/social-media/${id}/`,
    DELETE: (id: number) => `/social-media/${id}/`,
    REORDER: '/social-media/reorder/',
    ACTIVE: '/social-media/active/',
  },

  // Dashboard endpoints
  DASHBOARD: {
    OVERVIEW: '/dashboard/overview/',
    METRICS: '/dashboard/metrics/',
    RECENT_ACTIVITIES: '/dashboard/recent-activities/',
    ANALYTICS: '/dashboard/analytics/',
    STATS: {
      DEMO_REQUESTS: '/dashboard/stats/demo-requests/',
      SERVICES: '/dashboard/stats/services/',
      TECHNOLOGIES: '/dashboard/stats/technologies/',
      TEAM: '/dashboard/stats/team/',
      RESOURCES: '/dashboard/stats/resources/',
    },
  },

  // File upload endpoints
  UPLOADS: {
    IMAGES: '/uploads/images/',
    DOCUMENTS: '/uploads/documents/',
    AVATARS: '/uploads/avatars/',
  },

  // System endpoints
  SYSTEM: {
    HEALTH: '/system/health/',
    VERSION: '/system/version/',
    SETTINGS: '/system/settings/',
  },
} as const;

// Helper function to build query string
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, String(item)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
};

// Helper function to build endpoint with query params
export const buildEndpoint = (endpoint: string, params?: Record<string, any>): string => {
  if (!params) return endpoint;
  return endpoint + buildQueryString(params);
};

// Export endpoint groups for easier imports
export const {
  AUTH,
  DEMO_REQUESTS,
  SERVICES,
  TECHNOLOGIES,
  TEAM,
  RESOURCES,
  SOCIAL,
  DASHBOARD,
  UPLOADS,
  SYSTEM,
} = API_ENDPOINTS;
