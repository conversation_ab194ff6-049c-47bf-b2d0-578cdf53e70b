// Custom hook that combines technologies queries and mutations
// This demonstrates how to use the technologies service layer in components

import { useState, useCallback } from 'react';
import { useToast } from './use-toast';
import {
  useTechnologies,
  useTechnologyCategories,
  useCreateTechnology,
  useUpdateTechnology,
  useDeleteTechnology,
  useToggleTechnologyFeatured,
  useToggleTechnologyActive,
  useCreateTechnologyCategory,
  useUpdateTechnologyCategory,
  useDeleteTechnologyCategory,
  useReorderTechnologies,
  type TechnologyListParams,
  type CreateTechnology,
  type UpdateTechnology,
  type CreateTechnologyCategory,
  type UpdateTechnologyCategory,
  type ReorderTechnologiesRequest,
} from '@/services';

export const useTechnologiesWithActions = (initialParams?: TechnologyListParams) => {
  const [params, setParams] = useState<TechnologyListParams>(initialParams || {});
  const { toast } = useToast();

  // Queries
  const {
    data: technologiesData,
    isLoading: isTechnologiesLoading,
    isError: isTechnologiesError,
    error: technologiesError,
    refetch: refetchTechnologies,
  } = useTechnologies(params);

  const {
    data: categoriesData,
    isLoading: isCategoriesLoading,
    isError: isCategoriesError,
    error: categoriesError,
    refetch: refetchCategories,
  } = useTechnologyCategories();

  // Technology Mutations
  const createTechnologyMutation = useCreateTechnology();
  const updateTechnologyMutation = useUpdateTechnology();
  const deleteTechnologyMutation = useDeleteTechnology();
  const toggleFeaturedMutation = useToggleTechnologyFeatured();
  const toggleActiveMutation = useToggleTechnologyActive();
  const reorderMutation = useReorderTechnologies();

  // Category Mutations
  const createCategoryMutation = useCreateTechnologyCategory();
  const updateCategoryMutation = useUpdateTechnologyCategory();
  const deleteCategoryMutation = useDeleteTechnologyCategory();

  // Technology Actions
  const createTechnology = useCallback(async (data: CreateTechnology) => {
    try {
      const result = await createTechnologyMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Technology created successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create technology",
        variant: "destructive",
      });
      throw error;
    }
  }, [createTechnologyMutation, toast]);

  const updateTechnology = useCallback(async (data: UpdateTechnology) => {
    try {
      const result = await updateTechnologyMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Technology updated successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update technology",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateTechnologyMutation, toast]);

  const deleteTechnology = useCallback(async (id: number) => {
    try {
      await deleteTechnologyMutation.mutateAsync(id);
      toast({
        title: "Success",
        description: "Technology deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete technology",
        variant: "destructive",
      });
      throw error;
    }
  }, [deleteTechnologyMutation, toast]);

  const toggleFeatured = useCallback(async (id: number, is_featured: boolean) => {
    try {
      const result = await toggleFeaturedMutation.mutateAsync({ id, is_featured });
      toast({
        title: "Success",
        description: `Technology ${is_featured ? 'added to' : 'removed from'} featured`,
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update featured status",
        variant: "destructive",
      });
      throw error;
    }
  }, [toggleFeaturedMutation, toast]);

  const toggleActive = useCallback(async (id: number, is_active: boolean) => {
    try {
      const result = await toggleActiveMutation.mutateAsync({ id, is_active });
      toast({
        title: "Success",
        description: `Technology ${is_active ? 'activated' : 'deactivated'}`,
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update active status",
        variant: "destructive",
      });
      throw error;
    }
  }, [toggleActiveMutation, toast]);

  const reorderTechnologies = useCallback(async (data: ReorderTechnologiesRequest) => {
    try {
      const result = await reorderMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Technologies reordered successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reorder technologies",
        variant: "destructive",
      });
      throw error;
    }
  }, [reorderMutation, toast]);

  // Category Actions
  const createCategory = useCallback(async (data: CreateTechnologyCategory) => {
    try {
      const result = await createCategoryMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Category created successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create category",
        variant: "destructive",
      });
      throw error;
    }
  }, [createCategoryMutation, toast]);

  const updateCategory = useCallback(async (data: UpdateTechnologyCategory) => {
    try {
      const result = await updateCategoryMutation.mutateAsync(data);
      toast({
        title: "Success",
        description: "Category updated successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update category",
        variant: "destructive",
      });
      throw error;
    }
  }, [updateCategoryMutation, toast]);

  const deleteCategory = useCallback(async (id: number) => {
    try {
      await deleteCategoryMutation.mutateAsync(id);
      toast({
        title: "Success",
        description: "Category deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete category",
        variant: "destructive",
      });
      throw error;
    }
  }, [deleteCategoryMutation, toast]);

  // Filter and search functions
  const updateFilters = useCallback((newParams: Partial<TechnologyListParams>) => {
    setParams(prev => ({ ...prev, ...newParams, page: 1 })); // Reset to first page
  }, []);

  const clearFilters = useCallback(() => {
    setParams({});
  }, []);

  const setPage = useCallback((page: number) => {
    setParams(prev => ({ ...prev, page }));
  }, []);

  const setCategoryFilter = useCallback((category_id?: number) => {
    setParams(prev => ({ ...prev, category_id, page: 1 }));
  }, []);

  const setSearch = useCallback((search: string) => {
    setParams(prev => ({ ...prev, search, page: 1 }));
  }, []);

  const setActiveFilter = useCallback((is_active?: boolean) => {
    setParams(prev => ({ ...prev, is_active, page: 1 }));
  }, []);

  const setFeaturedFilter = useCallback((is_featured?: boolean) => {
    setParams(prev => ({ ...prev, is_featured, page: 1 }));
  }, []);

  const setOrdering = useCallback((ordering: string) => {
    setParams(prev => ({ ...prev, ordering, page: 1 }));
  }, []);

  return {
    // Data
    technologies: technologiesData?.results || [],
    totalTechnologiesCount: technologiesData?.count || 0,
    categories: categoriesData?.results || [],
    totalCategoriesCount: categoriesData?.count || 0,
    hasNextPage: !!technologiesData?.next,
    hasPreviousPage: !!technologiesData?.previous,
    
    // Loading states
    isLoading: isTechnologiesLoading || isCategoriesLoading,
    isTechnologiesLoading,
    isCategoriesLoading,
    isError: isTechnologiesError || isCategoriesError,
    error: technologiesError || categoriesError,
    
    // Technology mutation states
    isCreatingTechnology: createTechnologyMutation.isPending,
    isUpdatingTechnology: updateTechnologyMutation.isPending,
    isDeletingTechnology: deleteTechnologyMutation.isPending,
    isTogglingFeatured: toggleFeaturedMutation.isPending,
    isTogglingActive: toggleActiveMutation.isPending,
    isReordering: reorderMutation.isPending,
    
    // Category mutation states
    isCreatingCategory: createCategoryMutation.isPending,
    isUpdatingCategory: updateCategoryMutation.isPending,
    isDeletingCategory: deleteCategoryMutation.isPending,
    
    // Technology actions
    createTechnology,
    updateTechnology,
    deleteTechnology,
    toggleFeatured,
    toggleActive,
    reorderTechnologies,
    
    // Category actions
    createCategory,
    updateCategory,
    deleteCategory,
    
    // Refetch functions
    refetchTechnologies,
    refetchCategories,
    
    // Filters
    params,
    updateFilters,
    clearFilters,
    setPage,
    setCategoryFilter,
    setSearch,
    setActiveFilter,
    setFeaturedFilter,
    setOrdering,
  };
};
