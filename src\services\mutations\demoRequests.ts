// TanStack Query mutations for Demo Requests
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../api/client';
import { DEMO_REQUESTS } from '../api/endpoints';
import { QUERY_KEYS } from '../config';
import { handleApiError, getErrorMessage } from '../utils/errorHandling';
import type {
  DemoRequest,
  CreateDemoRequest,
  UpdateDemoRequest,
  BulkUpdateDemoRequestsRequest,
  BulkDeleteDemoRequestsRequest,
  ExportDemoRequestsRequest,
} from '../../types/demoRequest';

// Create new demo request
export const useCreateDemoRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateDemoRequest): Promise<DemoRequest> => {
      const response = await apiClient.post<DemoRequest>(
        DEMO_REQUESTS.CREATE,
        data
      );
      return response.data;
    },
    onSuccess: (newDemoRequest) => {
      // Invalidate and refetch demo requests list
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.ALL,
      });

      // Invalidate stats
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.STATS,
      });

      // Add the new demo request to existing cache if possible
      queryClient.setQueryData(
        QUERY_KEYS.DEMO_REQUESTS.DETAIL(newDemoRequest.id),
        newDemoRequest
      );
    },
    onError: (error) => {
      console.error('Failed to create demo request:', error);
      throw handleApiError(error);
    },
  });
};

// Update existing demo request
export const useUpdateDemoRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateDemoRequest): Promise<DemoRequest> => {
      const response = await apiClient.put<DemoRequest>(
        DEMO_REQUESTS.UPDATE(data.id),
        data
      );
      return response.data;
    },
    onMutate: async (updatedData) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.DETAIL(updatedData.id),
      });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData<DemoRequest>(
        QUERY_KEYS.DEMO_REQUESTS.DETAIL(updatedData.id)
      );

      // Optimistically update the cache
      if (previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.DEMO_REQUESTS.DETAIL(updatedData.id),
          { ...previousData, ...updatedData }
        );
      }

      return { previousData };
    },
    onSuccess: (updatedDemoRequest) => {
      // Update the specific demo request in cache
      queryClient.setQueryData(
        QUERY_KEYS.DEMO_REQUESTS.DETAIL(updatedDemoRequest.id),
        updatedDemoRequest
      );

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.ALL,
      });

      // Invalidate stats if status changed
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.STATS,
      });
    },
    onError: (error, updatedData, context) => {
      // Revert optimistic update on error
      if (context?.previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.DEMO_REQUESTS.DETAIL(updatedData.id),
          context.previousData
        );
      }
      console.error('Failed to update demo request:', error);
      throw handleApiError(error);
    },
  });
};

// Delete demo request
export const useDeleteDemoRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await apiClient.delete(DEMO_REQUESTS.DELETE(id));
    },
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.DETAIL(deletedId),
      });

      // Invalidate lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.ALL,
      });

      // Invalidate stats
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.STATS,
      });
    },
    onError: (error) => {
      console.error('Failed to delete demo request:', error);
      throw handleApiError(error);
    },
  });
};

// Bulk update demo requests
export const useBulkUpdateDemoRequests = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BulkUpdateDemoRequestsRequest): Promise<DemoRequest[]> => {
      const response = await apiClient.post<DemoRequest[]>(
        DEMO_REQUESTS.BULK_UPDATE,
        data
      );
      return response.data;
    },
    onSuccess: (updatedRequests) => {
      // Update individual items in cache
      updatedRequests.forEach((request) => {
        queryClient.setQueryData(
          QUERY_KEYS.DEMO_REQUESTS.DETAIL(request.id),
          request
        );
      });

      // Invalidate lists and stats
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.STATS,
      });
    },
    onError: (error) => {
      console.error('Failed to bulk update demo requests:', error);
      throw handleApiError(error);
    },
  });
};

// Bulk delete demo requests
export const useBulkDeleteDemoRequests = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BulkDeleteDemoRequestsRequest): Promise<void> => {
      await apiClient.post(DEMO_REQUESTS.BULK_DELETE, data);
    },
    onSuccess: (_, { ids }) => {
      // Remove from cache
      ids.forEach((id) => {
        queryClient.removeQueries({
          queryKey: QUERY_KEYS.DEMO_REQUESTS.DETAIL(id),
        });
      });

      // Invalidate lists and stats
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.STATS,
      });
    },
    onError: (error) => {
      console.error('Failed to bulk delete demo requests:', error);
      throw handleApiError(error);
    },
  });
};

// Export demo requests
export const useExportDemoRequests = () => {
  return useMutation({
    mutationFn: async (data: ExportDemoRequestsRequest): Promise<Blob> => {
      const response = await apiClient.post<Blob>(
        DEMO_REQUESTS.EXPORT,
        data
      );
      return response.data;
    },
    onError: (error) => {
      console.error('Failed to export demo requests:', error);
      throw handleApiError(error);
    },
  });
};

// Assign demo request to user
export const useAssignDemoRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, assignedTo }: { id: number; assignedTo: string }): Promise<DemoRequest> => {
      const response = await apiClient.patch<DemoRequest>(
        DEMO_REQUESTS.UPDATE(id),
        { assigned_to: assignedTo }
      );
      return response.data;
    },
    onSuccess: (updatedRequest) => {
      queryClient.setQueryData(
        QUERY_KEYS.DEMO_REQUESTS.DETAIL(updatedRequest.id),
        updatedRequest
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.ALL,
      });
    },
    onError: (error) => {
      console.error('Failed to assign demo request:', error);
      throw handleApiError(error);
    },
  });
};

// Update demo request status
export const useUpdateDemoRequestStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, status }: { id: number; status: string }): Promise<DemoRequest> => {
      const response = await apiClient.patch<DemoRequest>(
        DEMO_REQUESTS.UPDATE(id),
        { status }
      );
      return response.data;
    },
    onSuccess: (updatedRequest) => {
      queryClient.setQueryData(
        QUERY_KEYS.DEMO_REQUESTS.DETAIL(updatedRequest.id),
        updatedRequest
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.DEMO_REQUESTS.STATS,
      });
    },
    onError: (error) => {
      console.error('Failed to update demo request status:', error);
      throw handleApiError(error);
    },
  });
};
