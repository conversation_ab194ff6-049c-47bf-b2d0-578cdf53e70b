// TanStack Query mutations for Services
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../api/client';
import { SERVICES } from '../api/endpoints';
import { QUERY_KEYS } from '../config';
import { handleApiError } from '../utils/errorHandling';
import type { Service, CreateService, UpdateService } from '../../types/service';

// Bulk operations types
export interface BulkUpdateServicesRequest {
  ids: number[];
  data: {
    is_active?: boolean;
    is_featured?: boolean;
    category?: string;
  };
}

export interface BulkDeleteServicesRequest {
  ids: number[];
}

export interface ReorderServicesRequest {
  services: Array<{ id: number; display_order: number }>;
}

// Create new service
export const useCreateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateService): Promise<Service> => {
      const response = await apiClient.post<Service>(
        SERVICES.CREATE,
        data
      );
      return response.data;
    },
    onSuccess: (newService) => {
      // Invalidate and refetch services list
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.ALL,
      });

      // Invalidate categories if this is a new category
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.CATEGORIES,
      });

      // Add the new service to existing cache if possible
      queryClient.setQueryData(
        QUERY_KEYS.SERVICES.DETAIL(newService.id),
        newService
      );

      // Invalidate featured services if this service is featured
      if (newService.is_featured) {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.SERVICES.FEATURED,
        });
      }
    },
    onError: (error) => {
      console.error('Failed to create service:', error);
      throw handleApiError(error);
    },
  });
};

// Update existing service
export const useUpdateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateService): Promise<Service> => {
      const response = await apiClient.put<Service>(
        SERVICES.UPDATE(data.id),
        data
      );
      return response.data;
    },
    onMutate: async (updatedData) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QUERY_KEYS.SERVICES.DETAIL(updatedData.id),
      });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData<Service>(
        QUERY_KEYS.SERVICES.DETAIL(updatedData.id)
      );

      // Optimistically update the cache
      if (previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.SERVICES.DETAIL(updatedData.id),
          { ...previousData, ...updatedData }
        );
      }

      return { previousData };
    },
    onSuccess: (updatedService) => {
      // Update the specific service in cache
      queryClient.setQueryData(
        QUERY_KEYS.SERVICES.DETAIL(updatedService.id),
        updatedService
      );

      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.ALL,
      });

      // Invalidate featured services if featured status changed
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.FEATURED,
      });

      // Invalidate categories if category changed
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.CATEGORIES,
      });
    },
    onError: (error, updatedData, context) => {
      // Revert optimistic update on error
      if (context?.previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.SERVICES.DETAIL(updatedData.id),
          context.previousData
        );
      }
      console.error('Failed to update service:', error);
      throw handleApiError(error);
    },
  });
};

// Delete service
export const useDeleteService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await apiClient.delete(SERVICES.DELETE(id));
    },
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: QUERY_KEYS.SERVICES.DETAIL(deletedId),
      });

      // Invalidate lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.ALL,
      });

      // Invalidate featured services
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.FEATURED,
      });

      // Invalidate categories
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.CATEGORIES,
      });
    },
    onError: (error) => {
      console.error('Failed to delete service:', error);
      throw handleApiError(error);
    },
  });
};

// Toggle service featured status
export const useToggleServiceFeatured = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, is_featured }: { id: number; is_featured: boolean }): Promise<Service> => {
      const response = await apiClient.patch<Service>(
        SERVICES.UPDATE(id),
        { is_featured }
      );
      return response.data;
    },
    onMutate: async ({ id, is_featured }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QUERY_KEYS.SERVICES.DETAIL(id),
      });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData<Service>(
        QUERY_KEYS.SERVICES.DETAIL(id)
      );

      // Optimistically update the cache
      if (previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.SERVICES.DETAIL(id),
          { ...previousData, is_featured }
        );
      }

      return { previousData };
    },
    onSuccess: (updatedService) => {
      queryClient.setQueryData(
        QUERY_KEYS.SERVICES.DETAIL(updatedService.id),
        updatedService
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.FEATURED,
      });
    },
    onError: (error, { id }, context) => {
      // Revert optimistic update on error
      if (context?.previousData) {
        queryClient.setQueryData(
          QUERY_KEYS.SERVICES.DETAIL(id),
          context.previousData
        );
      }
      console.error('Failed to toggle service featured status:', error);
      throw handleApiError(error);
    },
  });
};

// Toggle service active status
export const useToggleServiceActive = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, is_active }: { id: number; is_active: boolean }): Promise<Service> => {
      const response = await apiClient.patch<Service>(
        SERVICES.UPDATE(id),
        { is_active }
      );
      return response.data;
    },
    onSuccess: (updatedService) => {
      queryClient.setQueryData(
        QUERY_KEYS.SERVICES.DETAIL(updatedService.id),
        updatedService
      );
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.ALL,
      });
    },
    onError: (error) => {
      console.error('Failed to toggle service active status:', error);
      throw handleApiError(error);
    },
  });
};

// Bulk update services
export const useBulkUpdateServices = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BulkUpdateServicesRequest): Promise<Service[]> => {
      const response = await apiClient.post<Service[]>(
        SERVICES.LIST, // Assuming bulk update endpoint
        data
      );
      return response.data;
    },
    onSuccess: (updatedServices) => {
      // Update individual items in cache
      updatedServices.forEach((service) => {
        queryClient.setQueryData(
          QUERY_KEYS.SERVICES.DETAIL(service.id),
          service
        );
      });

      // Invalidate lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.FEATURED,
      });
    },
    onError: (error) => {
      console.error('Failed to bulk update services:', error);
      throw handleApiError(error);
    },
  });
};

// Bulk delete services
export const useBulkDeleteServices = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BulkDeleteServicesRequest): Promise<void> => {
      await apiClient.post(SERVICES.LIST, data); // Assuming bulk delete endpoint
    },
    onSuccess: (_, { ids }) => {
      // Remove from cache
      ids.forEach((id) => {
        queryClient.removeQueries({
          queryKey: QUERY_KEYS.SERVICES.DETAIL(id),
        });
      });

      // Invalidate lists
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.FEATURED,
      });
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.CATEGORIES,
      });
    },
    onError: (error) => {
      console.error('Failed to bulk delete services:', error);
      throw handleApiError(error);
    },
  });
};

// Reorder services
export const useReorderServices = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ReorderServicesRequest): Promise<Service[]> => {
      const response = await apiClient.post<Service[]>(
        SERVICES.REORDER,
        data
      );
      return response.data;
    },
    onSuccess: (reorderedServices) => {
      // Update individual items in cache
      reorderedServices.forEach((service) => {
        queryClient.setQueryData(
          QUERY_KEYS.SERVICES.DETAIL(service.id),
          service
        );
      });

      // Invalidate lists to reflect new order
      queryClient.invalidateQueries({
        queryKey: QUERY_KEYS.SERVICES.ALL,
      });
    },
    onError: (error) => {
      console.error('Failed to reorder services:', error);
      throw handleApiError(error);
    },
  });
};
